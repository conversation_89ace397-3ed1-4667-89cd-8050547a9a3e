<?php
/**
 * Multiple Services Booking Functions
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/booking_functions.php';

/**
 * Create booking with multiple services
 */
function createBookingWithMultipleServices($userId, $data) {
    global $database;
    
    try {
        $database->beginTransaction();
        
        // Validate required fields
        if (empty($data['services']) || !is_array($data['services'])) {
            return ['success' => false, 'error' => 'At least one service must be selected'];
        }
        
        if (empty($data['staff_id']) || empty($data['date']) || empty($data['start_time'])) {
            return ['success' => false, 'error' => 'Staff, date, and start time are required'];
        }
        
        // Get service details and calculate totals
        $serviceDetails = [];
        $totalPrice = 0;
        $totalDuration = 0;
        
        foreach ($data['services'] as $serviceId) {
            $service = $database->fetch(
                "SELECT id, name, price, duration FROM services WHERE id = ? AND is_active = 1",
                [$serviceId]
            );
            
            if (!$service) {
                $database->rollback();
                return ['success' => false, 'error' => "Service not found: $serviceId"];
            }
            
            $serviceDetails[] = $service;
            $totalPrice += $service['price'];
            $totalDuration += $service['duration'];
        }
        
        // Calculate end time based on total duration
        $startTime = new DateTime($data['start_time']);
        $endTime = clone $startTime;
        $endTime->add(new DateInterval("PT{$totalDuration}M"));
        
        // Check staff availability for the extended duration
        $isAvailable = checkStaffAvailability(
            $data['staff_id'],
            $data['date'],
            $data['start_time'],
            $endTime->format('H:i:s')
        );
        
        if (!$isAvailable) {
            $database->rollback();
            return ['success' => false, 'error' => 'Staff is not available for the required time duration'];
        }
        
        // Apply points discount if specified
        $pointsUsed = intval($data['points_used'] ?? 0);
        $finalAmount = max(0, $totalPrice - ($pointsUsed * 10)); // 1 point = TSH 10
        
        // Calculate points earned (1 point per TSH 1,000 spent)
        $pointsEarned = max(0, floor($finalAmount / 1000));
        
        $bookingId = generateUUID();
        
        // Create main booking record
        $bookingData = [
            $bookingId,
            $userId,
            null, // service_id (null for multiple services)
            null, // package_id
            $data['staff_id'],
            null, // offer_id
            null, // recurring_series_id
            $data['date'],
            $data['start_time'],
            $endTime->format('H:i:s'),
            $finalAmount,
            $pointsUsed,
            $pointsEarned,
            0, // is_recurring
            1, // has_multiple_services
            sanitize($data['notes'] ?? '')
        ];
        
        $result = $database->query(
            "INSERT INTO bookings (id, user_id, service_id, package_id, staff_id, offer_id, recurring_series_id,
                                 date, start_time, end_time, total_amount, points_used, points_earned, 
                                 is_recurring, has_multiple_services, notes, status, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'PENDING', NOW(), NOW())",
            $bookingData
        );
        
        if (!$result) {
            $database->rollback();
            return ['success' => false, 'error' => 'Failed to create booking'];
        }
        
        // Insert individual services for this booking
        foreach ($serviceDetails as $service) {
            $serviceBookingId = generateUUID();
            
            $database->query(
                "INSERT INTO booking_services (id, booking_id, service_id, service_price, service_duration, created_at)
                 VALUES (?, ?, ?, ?, ?, NOW())",
                [
                    $serviceBookingId,
                    $bookingId,
                    $service['id'],
                    $service['price'],
                    $service['duration']
                ]
            );
        }
        
        // Update user points
        if ($pointsEarned > 0 || $pointsUsed > 0) {
            $pointsChange = $pointsEarned - $pointsUsed;
            try {
                $database->query(
                    "UPDATE users SET points = points + ? WHERE id = ?",
                    [$pointsChange, $userId]
                );
            } catch (Exception $e) {
                error_log("Failed to update user points: " . $e->getMessage());
                // Don't fail the booking creation for points update failure
            }
        }
        
        $database->commit();
        
        // Create notification for new booking
        require_once __DIR__ . '/notification_triggers.php';
        createBookingNotificationWithEmail($bookingId, 'BOOKING_NEW');
        
        // Schedule reminders for the new booking
        scheduleBookingReminders($bookingId);
        
        return [
            'success' => true, 
            'id' => $bookingId,
            'total_amount' => $finalAmount,
            'total_duration' => $totalDuration,
            'services_count' => count($serviceDetails)
        ];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Error creating booking with multiple services: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create booking with multiple services'];
    }
}

/**
 * Get services for a booking (handles both single and multiple services)
 */
function getBookingServices($bookingId) {
    global $database;
    
    // First check if this booking has multiple services
    $booking = $database->fetch(
        "SELECT has_multiple_services, service_id FROM bookings WHERE id = ?",
        [$bookingId]
    );
    
    if (!$booking) {
        return [];
    }
    
    if ($booking['has_multiple_services']) {
        // Get multiple services from booking_services table
        return $database->fetchAll(
            "SELECT bs.*, s.name, s.description, s.category, s.image
             FROM booking_services bs
             JOIN services s ON bs.service_id = s.id
             WHERE bs.booking_id = ?
             ORDER BY bs.created_at",
            [$bookingId]
        );
    } else if ($booking['service_id']) {
        // Get single service
        $service = $database->fetch(
            "SELECT id as service_id, name, description, category, image, price as service_price, duration as service_duration
             FROM services WHERE id = ?",
            [$booking['service_id']]
        );
        
        return $service ? [$service] : [];
    }
    
    return [];
}

/**
 * Calculate total duration and price for multiple services
 */
function calculateMultipleServicesTotal($serviceIds) {
    global $database;
    
    if (empty($serviceIds) || !is_array($serviceIds)) {
        return ['total_price' => 0, 'total_duration' => 0, 'services' => []];
    }
    
    $placeholders = str_repeat('?,', count($serviceIds) - 1) . '?';
    
    $services = $database->fetchAll(
        "SELECT id, name, price, duration FROM services 
         WHERE id IN ($placeholders) AND is_active = 1",
        $serviceIds
    );
    
    $totalPrice = 0;
    $totalDuration = 0;
    
    foreach ($services as $service) {
        $totalPrice += $service['price'];
        $totalDuration += $service['duration'];
    }
    
    return [
        'total_price' => $totalPrice,
        'total_duration' => $totalDuration,
        'services' => $services
    ];
}

/**
 * Check if staff is available for extended duration (multiple services)
 */
function checkStaffAvailabilityForMultipleServices($staffId, $date, $startTime, $serviceIds) {
    global $database;
    
    // Calculate total duration
    $totals = calculateMultipleServicesTotal($serviceIds);
    $totalDuration = $totals['total_duration'];
    
    // Calculate end time
    $startDateTime = new DateTime("$date $startTime");
    $endDateTime = clone $startDateTime;
    $endDateTime->add(new DateInterval("PT{$totalDuration}M"));
    
    return checkStaffAvailability($staffId, $date, $startTime, $endDateTime->format('H:i:s'));
}

/**
 * Get available time slots for multiple services
 */
function getAvailableTimeSlotsForMultipleServices($date, $serviceIds, $staffId) {
    global $database;
    
    // Calculate total duration needed
    $totals = calculateMultipleServicesTotal($serviceIds);
    $totalDuration = $totals['total_duration'];
    
    if ($totalDuration === 0) {
        return [];
    }
    
    // Get staff working hours for the date
    $dayOfWeek = strtoupper(date('l', strtotime($date)));
    
    $workingHours = $database->fetch(
        "SELECT start_time, end_time FROM staff_working_hours 
         WHERE staff_id = ? AND day_of_week = ? AND is_active = 1",
        [$staffId, $dayOfWeek]
    );
    
    if (!$workingHours) {
        return []; // Staff doesn't work on this day
    }
    
    // Get existing bookings for the staff on this date
    $existingBookings = $database->fetchAll(
        "SELECT start_time, end_time FROM bookings 
         WHERE staff_id = ? AND date = ? AND status IN ('CONFIRMED', 'PENDING', 'IN_PROGRESS')
         ORDER BY start_time",
        [$staffId, $date]
    );
    
    // Generate available time slots
    $slots = [];
    $slotDuration = 30; // 30-minute intervals
    $currentTime = new DateTime($workingHours['start_time']);
    $endTime = new DateTime($workingHours['end_time']);
    
    while ($currentTime < $endTime) {
        $slotEndTime = clone $currentTime;
        $slotEndTime->add(new DateInterval("PT{$totalDuration}M"));
        
        // Check if this slot would extend beyond working hours
        if ($slotEndTime > $endTime) {
            break;
        }
        
        // Check if this slot conflicts with existing bookings
        $hasConflict = false;
        foreach ($existingBookings as $booking) {
            $bookingStart = new DateTime($booking['start_time']);
            $bookingEnd = new DateTime($booking['end_time']);
            
            // Check for overlap
            if ($currentTime < $bookingEnd && $slotEndTime > $bookingStart) {
                $hasConflict = true;
                break;
            }
        }
        
        if (!$hasConflict) {
            $slots[] = [
                'start_time' => $currentTime->format('H:i'),
                'end_time' => $slotEndTime->format('H:i'),
                'duration' => $totalDuration
            ];
        }
        
        $currentTime->add(new DateInterval("PT{$slotDuration}M"));
    }
    
    return $slots;
}

/**
 * Update booking with multiple services
 */
function updateBookingWithMultipleServices($bookingId, $data) {
    global $database;
    
    try {
        $database->beginTransaction();
        
        // Get current booking
        $currentBooking = $database->fetch(
            "SELECT * FROM bookings WHERE id = ?",
            [$bookingId]
        );
        
        if (!$currentBooking) {
            $database->rollback();
            return ['success' => false, 'error' => 'Booking not found'];
        }
        
        // If services are being updated
        if (isset($data['services']) && is_array($data['services'])) {
            // Remove existing service associations
            $database->query(
                "DELETE FROM booking_services WHERE booking_id = ?",
                [$bookingId]
            );
            
            // Calculate new totals
            $totals = calculateMultipleServicesTotal($data['services']);
            
            // Update booking with new totals
            $startTime = new DateTime($data['start_time'] ?? $currentBooking['start_time']);
            $endTime = clone $startTime;
            $endTime->add(new DateInterval("PT{$totals['total_duration']}M"));
            
            $database->query(
                "UPDATE bookings 
                 SET total_amount = ?, end_time = ?, has_multiple_services = 1, updated_at = NOW()
                 WHERE id = ?",
                [$totals['total_price'], $endTime->format('H:i:s'), $bookingId]
            );
            
            // Add new service associations
            foreach ($totals['services'] as $service) {
                $serviceBookingId = generateUUID();
                
                $database->query(
                    "INSERT INTO booking_services (id, booking_id, service_id, service_price, service_duration, created_at)
                     VALUES (?, ?, ?, ?, ?, NOW())",
                    [
                        $serviceBookingId,
                        $bookingId,
                        $service['id'],
                        $service['price'],
                        $service['duration']
                    ]
                );
            }
        }
        
        $database->commit();
        return ['success' => true, 'message' => 'Booking updated successfully'];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Error updating booking with multiple services: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update booking'];
    }
}

/**
 * Get booking details with services (for display purposes)
 */
function getBookingWithServices($bookingId) {
    global $database;
    
    // Get main booking details
    $booking = $database->fetch(
        "SELECT b.*, 
                u.name as customer_name, u.email as customer_email,
                st.name as staff_name, st.phone as staff_phone
         FROM bookings b
         LEFT JOIN users u ON b.user_id = u.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         WHERE b.id = ?",
        [$bookingId]
    );
    
    if (!$booking) {
        return null;
    }
    
    // Get associated services
    $booking['services'] = getBookingServices($bookingId);
    
    return $booking;
}
