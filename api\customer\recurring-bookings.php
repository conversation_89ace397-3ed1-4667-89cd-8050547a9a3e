<?php
/**
 * Customer Recurring Bookings API
 * Flix Salonce - PHP Version
 */

header('Content-Type: application/json');
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/recurring_booking_functions.php';

// Check if user is logged in and is a customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$userId = $_SESSION['user_id'];

try {
    switch ($method) {
        case 'GET':
            handleGetRecurringBookings($userId);
            break;
            
        case 'POST':
            handleCreateRecurringBooking($userId);
            break;
            
        case 'PUT':
            handleUpdateRecurringBooking($userId);
            break;
            
        case 'DELETE':
            handleDeleteRecurringBooking($userId);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetRecurringBookings($userId) {
    $includeInactive = $_GET['include_inactive'] ?? false;
    $series = getUserRecurringBookingSeries($userId, $includeInactive);
    
    echo json_encode([
        'success' => true,
        'series' => $series
    ]);
}

function handleCreateRecurringBooking($userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit;
    }
    
    // Validate required fields
    $requiredFields = ['staff_id', 'start_date', 'start_time', 'end_time', 'recurrence_type', 'total_amount'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            exit;
        }
    }
    
    // Ensure either service or package is selected
    if (empty($input['service_id']) && empty($input['package_id']) && empty($input['services'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Either service_id, package_id, or services array is required']);
        exit;
    }
    
    // Handle multiple services
    if (!empty($input['services']) && is_array($input['services'])) {
        $result = createRecurringBookingWithMultipleServices($userId, $input);
    } else {
        $result = createRecurringBookingSeries($userId, $input);
    }
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

function handleUpdateRecurringBooking($userId) {
    $seriesId = $_GET['series_id'] ?? '';
    
    if (empty($seriesId)) {
        http_response_code(400);
        echo json_encode(['error' => 'Series ID is required']);
        exit;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit;
    }
    
    $result = updateRecurringBookingSeries($seriesId, $userId, $input);
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

function handleDeleteRecurringBooking($userId) {
    $seriesId = $_GET['series_id'] ?? '';
    $cancelFuture = $_GET['cancel_future'] ?? true;
    
    if (empty($seriesId)) {
        http_response_code(400);
        echo json_encode(['error' => 'Series ID is required']);
        exit;
    }
    
    // Verify ownership
    global $database;
    $series = $database->fetch(
        "SELECT id FROM recurring_booking_series WHERE id = ? AND user_id = ?",
        [$seriesId, $userId]
    );
    
    if (!$series) {
        http_response_code(404);
        echo json_encode(['error' => 'Recurring booking series not found']);
        exit;
    }
    
    $result = cancelRecurringBookingSeries($seriesId, $cancelFuture);
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

/**
 * Create recurring booking with multiple services
 */
function createRecurringBookingWithMultipleServices($userId, $data) {
    global $database;
    
    try {
        require_once __DIR__ . '/../../includes/multiple_services_functions.php';
        
        // Calculate totals for multiple services
        $totals = calculateMultipleServicesTotal($data['services']);
        
        if ($totals['total_duration'] === 0) {
            return ['success' => false, 'error' => 'No valid services selected'];
        }
        
        // Calculate end time based on total duration
        $startTime = new DateTime($data['start_time']);
        $endTime = clone $startTime;
        $endTime->add(new DateInterval("PT{$totals['total_duration']}M"));
        
        // Update data with calculated values
        $data['end_time'] = $endTime->format('H:i:s');
        $data['total_amount'] = $totals['total_price'];
        $data['service_id'] = null; // Multiple services, so no single service_id
        
        $database->beginTransaction();
        
        // Create the recurring series
        $result = createRecurringBookingSeries($userId, $data);
        
        if (!$result['success']) {
            $database->rollback();
            return $result;
        }
        
        $seriesId = $result['series_id'];
        
        // Get all bookings created for this series and add service associations
        $bookings = $database->fetchAll(
            "SELECT id FROM bookings WHERE recurring_series_id = ?",
            [$seriesId]
        );
        
        foreach ($bookings as $booking) {
            // Mark booking as having multiple services
            $database->query(
                "UPDATE bookings SET has_multiple_services = 1 WHERE id = ?",
                [$booking['id']]
            );
            
            // Add service associations
            foreach ($totals['services'] as $service) {
                $serviceBookingId = generateUUID();
                
                $database->query(
                    "INSERT INTO booking_services (id, booking_id, service_id, service_price, service_duration, created_at)
                     VALUES (?, ?, ?, ?, ?, NOW())",
                    [
                        $serviceBookingId,
                        $booking['id'],
                        $service['id'],
                        $service['price'],
                        $service['duration']
                    ]
                );
            }
        }
        
        $database->commit();
        
        return [
            'success' => true,
            'series_id' => $seriesId,
            'generated_bookings' => count($bookings),
            'total_duration' => $totals['total_duration'],
            'services_count' => count($totals['services']),
            'message' => "Recurring booking series created with " . count($bookings) . " appointments and " . count($totals['services']) . " services each"
        ];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Error creating recurring booking with multiple services: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create recurring booking with multiple services'];
    }
}

/**
 * Update recurring booking series
 */
function updateRecurringBookingSeries($seriesId, $userId, $data) {
    global $database;
    
    try {
        // Verify ownership
        $series = $database->fetch(
            "SELECT * FROM recurring_booking_series WHERE id = ? AND user_id = ?",
            [$seriesId, $userId]
        );
        
        if (!$series) {
            return ['success' => false, 'error' => 'Recurring booking series not found'];
        }
        
        $database->beginTransaction();
        
        // Update series details
        $updateFields = [];
        $updateParams = [];
        
        if (isset($data['title'])) {
            $updateFields[] = "title = ?";
            $updateParams[] = sanitize($data['title']);
        }
        
        if (isset($data['notes'])) {
            $updateFields[] = "notes = ?";
            $updateParams[] = sanitize($data['notes']);
        }
        
        if (isset($data['end_date'])) {
            $updateFields[] = "end_date = ?";
            $updateParams[] = $data['end_date'];
        }
        
        if (isset($data['max_occurrences'])) {
            $updateFields[] = "max_occurrences = ?";
            $updateParams[] = intval($data['max_occurrences']);
        }
        
        if (!empty($updateFields)) {
            $updateFields[] = "updated_at = NOW()";
            $updateParams[] = $seriesId;
            
            $database->query(
                "UPDATE recurring_booking_series SET " . implode(', ', $updateFields) . " WHERE id = ?",
                $updateParams
            );
        }
        
        $database->commit();
        
        return ['success' => true, 'message' => 'Recurring booking series updated successfully'];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Error updating recurring booking series: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to update recurring booking series'];
    }
}
