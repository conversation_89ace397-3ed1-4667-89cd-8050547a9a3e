<?php
/**
 * Recurring Booking Functions
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/booking_functions.php';

/**
 * Create a recurring booking series
 */
function createRecurringBookingSeries($userId, $data) {
    global $database;
    
    try {
        $database->beginTransaction();
        
        // Validate required fields
        $requiredFields = ['service_id', 'staff_id', 'start_date', 'start_time', 'end_time', 'recurrence_type', 'total_amount'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field]) && $field !== 'service_id') {
                return ['success' => false, 'error' => "Missing required field: $field"];
            }
        }
        
        // Ensure either service or package is selected
        if (empty($data['service_id']) && empty($data['package_id'])) {
            return ['success' => false, 'error' => 'Either service_id or package_id is required'];
        }
        
        // Validate recurrence type
        $validRecurrenceTypes = ['WEEKLY', 'BI_WEEKLY', 'MONTHLY'];
        if (!in_array($data['recurrence_type'], $validRecurrenceTypes)) {
            return ['success' => false, 'error' => 'Invalid recurrence type'];
        }
        
        // Validate end condition (either end_date or max_occurrences)
        if (empty($data['end_date']) && empty($data['max_occurrences'])) {
            return ['success' => false, 'error' => 'Either end_date or max_occurrences is required'];
        }
        
        $seriesId = generateUUID();
        
        // Create recurring series record
        $database->query(
            "INSERT INTO recurring_booking_series 
             (id, user_id, service_id, package_id, staff_id, title, recurrence_type, start_date, end_date, 
              max_occurrences, start_time, end_time, total_amount, points_used, notes, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $seriesId,
                $userId,
                $data['service_id'] ?? null,
                $data['package_id'] ?? null,
                $data['staff_id'],
                sanitize($data['title'] ?? 'Recurring Appointment'),
                $data['recurrence_type'],
                $data['start_date'],
                $data['end_date'] ?? null,
                $data['max_occurrences'] ?? null,
                $data['start_time'],
                $data['end_time'],
                floatval($data['total_amount']),
                intval($data['points_used'] ?? 0),
                sanitize($data['notes'] ?? '')
            ]
        );
        
        // Generate individual bookings for the series
        $generatedBookings = generateRecurringBookings($seriesId);
        
        $database->commit();
        
        return [
            'success' => true, 
            'series_id' => $seriesId,
            'generated_bookings' => $generatedBookings['count'],
            'message' => "Recurring booking series created with {$generatedBookings['count']} appointments"
        ];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Error creating recurring booking series: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create recurring booking series'];
    }
}

/**
 * Generate individual bookings from a recurring series
 */
function generateRecurringBookings($seriesId) {
    global $database;
    
    try {
        // Get series details
        $series = $database->fetch(
            "SELECT * FROM recurring_booking_series WHERE id = ? AND is_active = 1",
            [$seriesId]
        );
        
        if (!$series) {
            return ['success' => false, 'error' => 'Recurring series not found'];
        }
        
        $bookingDates = calculateRecurringDates($series);
        $generatedCount = 0;
        $errors = [];
        
        foreach ($bookingDates as $date) {
            // Check if booking already exists for this date
            $existingBooking = $database->fetch(
                "SELECT id FROM bookings WHERE recurring_series_id = ? AND date = ?",
                [$seriesId, $date]
            );
            
            if ($existingBooking) {
                continue; // Skip if booking already exists
            }
            
            // Check staff availability
            $isAvailable = checkStaffAvailability(
                $series['staff_id'],
                $date,
                $series['start_time'],
                $series['end_time']
            );
            
            if (!$isAvailable) {
                $errors[] = "Staff not available on $date";
                continue;
            }
            
            // Create individual booking
            $bookingData = [
                'service_id' => $series['service_id'],
                'package_id' => $series['package_id'],
                'staff_id' => $series['staff_id'],
                'date' => $date,
                'start_time' => $series['start_time'],
                'end_time' => $series['end_time'],
                'total_amount' => $series['total_amount'],
                'points_used' => $series['points_used'],
                'notes' => $series['notes'],
                'is_recurring' => true,
                'recurring_series_id' => $seriesId
            ];
            
            $result = createBookingFromRecurringSeries($series['user_id'], $bookingData);
            
            if ($result['success']) {
                $generatedCount++;
            } else {
                $errors[] = "Failed to create booking for $date: " . $result['error'];
            }
        }
        
        return [
            'success' => true,
            'count' => $generatedCount,
            'errors' => $errors
        ];
        
    } catch (Exception $e) {
        error_log("Error generating recurring bookings: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to generate recurring bookings'];
    }
}

/**
 * Calculate recurring dates based on series configuration
 */
function calculateRecurringDates($series) {
    $dates = [];
    $currentDate = new DateTime($series['start_date']);
    $endDate = $series['end_date'] ? new DateTime($series['end_date']) : null;
    $maxOccurrences = $series['max_occurrences'];
    $count = 0;
    
    // Determine interval based on recurrence type
    switch ($series['recurrence_type']) {
        case 'WEEKLY':
            $interval = new DateInterval('P1W');
            break;
        case 'BI_WEEKLY':
            $interval = new DateInterval('P2W');
            break;
        case 'MONTHLY':
            $interval = new DateInterval('P1M');
            break;
        default:
            return $dates;
    }
    
    while (true) {
        // Check end conditions
        if ($endDate && $currentDate > $endDate) {
            break;
        }
        
        if ($maxOccurrences && $count >= $maxOccurrences) {
            break;
        }
        
        // Don't create bookings in the past
        if ($currentDate >= new DateTime('today')) {
            $dates[] = $currentDate->format('Y-m-d');
            $count++;
        }
        
        $currentDate->add($interval);
        
        // Safety check to prevent infinite loops
        if ($count > 100) {
            break;
        }
    }
    
    return $dates;
}

/**
 * Create booking from recurring series (similar to regular booking but with recurring flags)
 */
function createBookingFromRecurringSeries($userId, $data) {
    global $database;
    
    try {
        // Calculate points earned (1 point per TSH 1,000 spent)
        $pointsUsed = intval($data['points_used'] ?? 0);
        $pointsEarned = max(0, floor(($data['total_amount'] - ($pointsUsed * 10)) / 1000));
        
        $bookingId = generateUUID();

        // Prepare booking data
        $bookingData = [
            $bookingId,
            $userId,
            $data['service_id'],
            $data['package_id'] ?? null,
            $data['staff_id'],
            null, // offer_id
            $data['recurring_series_id'],
            $data['date'],
            $data['start_time'],
            $data['end_time'],
            floatval($data['total_amount']),
            $pointsUsed,
            $pointsEarned,
            $data['is_recurring'] ? 1 : 0,
            $data['has_multiple_services'] ?? 0,
            sanitize($data['notes'] ?? '')
        ];

        // Insert booking
        $result = $database->query(
            "INSERT INTO bookings (id, user_id, service_id, package_id, staff_id, offer_id, recurring_series_id,
                                 date, start_time, end_time, total_amount, points_used, points_earned, 
                                 is_recurring, has_multiple_services, notes, status, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'PENDING', NOW(), NOW())",
            $bookingData
        );

        if (!$result) {
            return ['success' => false, 'error' => 'Failed to insert booking into database'];
        }

        // Schedule reminders for the new booking
        scheduleBookingReminders($bookingId);

        return ['success' => true, 'id' => $bookingId];
        
    } catch (Exception $e) {
        error_log("Error creating booking from recurring series: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to create booking'];
    }
}

/**
 * Get recurring booking series for a user
 */
function getUserRecurringBookingSeries($userId, $includeInactive = false) {
    global $database;
    
    $whereClause = "WHERE user_id = ?";
    $params = [$userId];
    
    if (!$includeInactive) {
        $whereClause .= " AND is_active = 1";
    }
    
    return $database->fetchAll(
        "SELECT rbs.*, 
                s.name as service_name, s.duration as service_duration,
                p.name as package_name, p.price as package_price,
                st.name as staff_name,
                COUNT(b.id) as total_bookings,
                COUNT(CASE WHEN b.status = 'COMPLETED' THEN 1 END) as completed_bookings,
                COUNT(CASE WHEN b.status IN ('PENDING', 'CONFIRMED') THEN 1 END) as upcoming_bookings
         FROM recurring_booking_series rbs
         LEFT JOIN services s ON rbs.service_id = s.id
         LEFT JOIN packages p ON rbs.package_id = p.id
         LEFT JOIN users st ON rbs.staff_id = st.id AND st.role = 'STAFF'
         LEFT JOIN bookings b ON rbs.id = b.recurring_series_id
         $whereClause
         GROUP BY rbs.id
         ORDER BY rbs.created_at DESC",
        $params
    );
}

/**
 * Cancel recurring booking series
 */
function cancelRecurringBookingSeries($seriesId, $cancelFutureBookings = true) {
    global $database;
    
    try {
        $database->beginTransaction();
        
        // Deactivate the series
        $database->query(
            "UPDATE recurring_booking_series SET is_active = 0, updated_at = NOW() WHERE id = ?",
            [$seriesId]
        );
        
        if ($cancelFutureBookings) {
            // Cancel all future bookings in the series
            $database->query(
                "UPDATE bookings 
                 SET status = 'CANCELLED', updated_at = NOW() 
                 WHERE recurring_series_id = ? 
                 AND status IN ('PENDING', 'CONFIRMED') 
                 AND date >= CURDATE()",
                [$seriesId]
            );
        }
        
        $database->commit();
        return ['success' => true, 'message' => 'Recurring booking series cancelled successfully'];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Error cancelling recurring booking series: " . $e->getMessage());
        return ['success' => false, 'error' => 'Failed to cancel recurring booking series'];
    }
}
