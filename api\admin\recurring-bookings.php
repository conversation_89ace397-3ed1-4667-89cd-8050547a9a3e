<?php
/**
 * Admin Recurring Bookings API
 * Flix Salonce - PHP Version
 */

header('Content-Type: application/json');
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/recurring_booking_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGetRequests($action);
            break;
            
        case 'POST':
            handlePostRequests($action);
            break;
            
        case 'PUT':
            handlePutRequests($action);
            break;
            
        case 'DELETE':
            handleDeleteRequests($action);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetRequests($action) {
    global $database;
    
    switch ($action) {
        case 'stats':
            handleGetStats();
            break;
            
        case 'series':
            handleGetSeries();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handlePostRequests($action) {
    switch ($action) {
        case 'cleanup':
            handleCleanupExpiredSeries();
            break;
            
        case 'generate':
            handleGenerateBookings();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handlePutRequests($action) {
    switch ($action) {
        case 'update_series':
            handleUpdateSeries();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handleDeleteRequests($action) {
    switch ($action) {
        case 'deactivate_series':
            handleDeactivateSeries();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
}

function handleGetStats() {
    global $database;
    
    $stats = [
        'total_series' => $database->fetch("SELECT COUNT(*) as count FROM recurring_booking_series")['count'],
        'active_series' => $database->fetch("SELECT COUNT(*) as count FROM recurring_booking_series WHERE is_active = 1")['count'],
        'total_recurring_bookings' => $database->fetch("SELECT COUNT(*) as count FROM bookings WHERE is_recurring = 1")['count'],
        'upcoming_recurring_bookings' => $database->fetch(
            "SELECT COUNT(*) as count FROM bookings 
             WHERE is_recurring = 1 AND status IN ('PENDING', 'CONFIRMED') AND date >= CURDATE()"
        )['count']
    ];
    
    // Get series by recurrence type
    $recurrenceStats = $database->fetchAll(
        "SELECT recurrence_type, COUNT(*) as count 
         FROM recurring_booking_series 
         WHERE is_active = 1 
         GROUP BY recurrence_type"
    );
    
    $stats['by_recurrence_type'] = [];
    foreach ($recurrenceStats as $stat) {
        $stats['by_recurrence_type'][$stat['recurrence_type']] = $stat['count'];
    }
    
    echo json_encode([
        'success' => true,
        'stats' => $stats
    ]);
}

function handleGetSeries() {
    global $database;
    
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    $search = sanitize($_GET['search'] ?? '');
    $status = $_GET['status'] ?? '';
    
    $whereClause = "WHERE 1=1";
    $params = [];
    
    if ($search) {
        $whereClause .= " AND (rbs.title LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if ($status === 'active') {
        $whereClause .= " AND rbs.is_active = 1";
    } elseif ($status === 'inactive') {
        $whereClause .= " AND rbs.is_active = 0";
    }
    
    $series = $database->fetchAll(
        "SELECT rbs.*, 
                u.name as customer_name, u.email as customer_email,
                s.name as service_name, s.duration as service_duration,
                p.name as package_name,
                st.name as staff_name,
                COUNT(b.id) as total_bookings,
                COUNT(CASE WHEN b.status = 'COMPLETED' THEN 1 END) as completed_bookings,
                COUNT(CASE WHEN b.status IN ('PENDING', 'CONFIRMED') THEN 1 END) as upcoming_bookings
         FROM recurring_booking_series rbs
         LEFT JOIN users u ON rbs.user_id = u.id
         LEFT JOIN services s ON rbs.service_id = s.id
         LEFT JOIN packages p ON rbs.package_id = p.id
         LEFT JOIN users st ON rbs.staff_id = st.id AND st.role = 'STAFF'
         LEFT JOIN bookings b ON rbs.id = b.recurring_series_id
         $whereClause
         GROUP BY rbs.id
         ORDER BY rbs.created_at DESC
         LIMIT $limit OFFSET $offset",
        $params
    );
    
    $totalSeries = $database->fetch(
        "SELECT COUNT(DISTINCT rbs.id) as count 
         FROM recurring_booking_series rbs
         LEFT JOIN users u ON rbs.user_id = u.id
         $whereClause",
        $params
    )['count'];
    
    echo json_encode([
        'success' => true,
        'series' => $series,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => ceil($totalSeries / $limit),
            'total_items' => $totalSeries,
            'items_per_page' => $limit
        ]
    ]);
}

function handleCleanupExpiredSeries() {
    global $database;
    
    try {
        $database->beginTransaction();
        
        // Deactivate series that have passed their end date
        $endDateResult = $database->query(
            "UPDATE recurring_booking_series 
             SET is_active = FALSE, updated_at = NOW()
             WHERE is_active = TRUE 
             AND end_date IS NOT NULL 
             AND end_date < CURDATE()"
        );
        
        $endDateCount = $database->rowCount();
        
        // Deactivate series that have reached their max occurrences
        $maxOccurrencesResult = $database->query(
            "UPDATE recurring_booking_series rbs
             SET is_active = FALSE, updated_at = NOW()
             WHERE is_active = TRUE 
             AND max_occurrences IS NOT NULL
             AND (
                 SELECT COUNT(*) 
                 FROM bookings b 
                 WHERE b.recurring_series_id = rbs.id 
                 AND b.status NOT IN ('CANCELLED', 'NO_SHOW')
             ) >= max_occurrences"
        );
        
        $maxOccurrencesCount = $database->rowCount();
        
        $database->commit();
        
        $totalDeactivated = $endDateCount + $maxOccurrencesCount;
        
        echo json_encode([
            'success' => true,
            'deactivated' => $totalDeactivated,
            'by_end_date' => $endDateCount,
            'by_max_occurrences' => $maxOccurrencesCount,
            'message' => "Deactivated $totalDeactivated expired recurring series"
        ]);
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

function handleGenerateBookings() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['series_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Series ID is required']);
        exit;
    }
    
    $result = generateRecurringBookings($input['series_id']);
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

function handleUpdateSeries() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['series_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Series ID is required']);
        exit;
    }
    
    global $database;
    
    try {
        $database->beginTransaction();
        
        $updateFields = [];
        $updateParams = [];
        
        if (isset($input['title'])) {
            $updateFields[] = "title = ?";
            $updateParams[] = sanitize($input['title']);
        }
        
        if (isset($input['notes'])) {
            $updateFields[] = "notes = ?";
            $updateParams[] = sanitize($input['notes']);
        }
        
        if (isset($input['end_date'])) {
            $updateFields[] = "end_date = ?";
            $updateParams[] = $input['end_date'];
        }
        
        if (isset($input['max_occurrences'])) {
            $updateFields[] = "max_occurrences = ?";
            $updateParams[] = intval($input['max_occurrences']);
        }
        
        if (isset($input['is_active'])) {
            $updateFields[] = "is_active = ?";
            $updateParams[] = $input['is_active'] ? 1 : 0;
        }
        
        if (!empty($updateFields)) {
            $updateFields[] = "updated_at = NOW()";
            $updateParams[] = $input['series_id'];
            
            $database->query(
                "UPDATE recurring_booking_series SET " . implode(', ', $updateFields) . " WHERE id = ?",
                $updateParams
            );
        }
        
        $database->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Recurring series updated successfully'
        ]);
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

function handleDeactivateSeries() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['series_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Series ID is required']);
        exit;
    }
    
    $cancelFutureBookings = $input['cancel_future_bookings'] ?? false;
    
    $result = cancelRecurringBookingSeries($input['series_id'], $cancelFutureBookings);
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}
