-- Booking System Enhancements: Recurring Bookings & Multiple Services
-- Execute this script to add support for recurring bookings and multiple services per booking

-- 1. Add new columns to existing bookings table
ALTER TABLE bookings ADD COLUMN recurring_series_id VARCHAR(36) NULL;
ALTER TABLE bookings ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE;
ALTER TABLE bookings ADD COLUMN has_multiple_services BOOLEAN DEFAULT FALSE;

-- 2. Create recurring booking series table
CREATE TABLE IF NOT EXISTS recurring_booking_series (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36),
    package_id VARCHAR(36),
    staff_id VARCHAR(36),
    title VARCHAR(255) NOT NULL,
    recurrence_type ENUM('WEEKLY', 'BI_WEEKLY', 'MONTHLY') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    max_occurrences INT,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    points_used INT DEFAULT 0,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL,
    FOREIGN KEY (staff_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 3. Create booking services junction table for multiple services per booking
CREATE TABLE IF NOT EXISTS booking_services (
    id VARCHAR(36) PRIMARY KEY,
    booking_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36) NOT NULL,
    service_price DECIMAL(10,2) NOT NULL,
    service_duration INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_booking_service (booking_id, service_id)
);

-- 4. Add foreign key constraint for recurring_series_id
ALTER TABLE bookings ADD CONSTRAINT fk_bookings_recurring_series FOREIGN KEY (recurring_series_id) REFERENCES recurring_booking_series(id) ON DELETE SET NULL;

-- 5. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_recurring_series ON bookings(recurring_series_id);
CREATE INDEX IF NOT EXISTS idx_bookings_is_recurring ON bookings(is_recurring);
CREATE INDEX IF NOT EXISTS idx_bookings_has_multiple_services ON bookings(has_multiple_services);
CREATE INDEX IF NOT EXISTS idx_recurring_series_user ON recurring_booking_series(user_id);
CREATE INDEX IF NOT EXISTS idx_recurring_series_active ON recurring_booking_series(is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_series_dates ON recurring_booking_series(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_booking_services_booking ON booking_services(booking_id);
CREATE INDEX IF NOT EXISTS idx_booking_services_service ON booking_services(service_id);

-- 6. Create a view for easy booking details with services
CREATE OR REPLACE VIEW booking_details_with_services AS
SELECT
    b.*,
    u.name as customer_name,
    u.email as customer_email,
    u.phone as customer_phone,
    st.name as staff_name,
    st.email as staff_email,
    st.phone as staff_phone,
    s.name as service_name,
    s.duration as service_duration,
    s.price as service_price,
    p.name as package_name,
    p.price as package_price,
    rbs.title as recurring_series_title,
    rbs.recurrence_type,
    rbs.is_active as series_active
FROM bookings b
LEFT JOIN users u ON b.user_id = u.id
LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
LEFT JOIN services s ON b.service_id = s.id
LEFT JOIN packages p ON b.package_id = p.id
LEFT JOIN recurring_booking_series rbs ON b.recurring_series_id = rbs.id;

-- 7. Create a trigger to maintain data consistency
CREATE TRIGGER tr_booking_services_update_booking
AFTER INSERT ON booking_services
FOR EACH ROW
UPDATE bookings
SET has_multiple_services = TRUE, updated_at = NOW()
WHERE id = NEW.booking_id;

-- Verify the changes
SELECT 'Booking system enhancements applied successfully!' as status;
