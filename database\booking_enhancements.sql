-- Booking System Enhancements: Recurring Bookings & Multiple Services
-- Execute this script to add support for recurring bookings and multiple services per booking

USE flix_salonce2;

-- 1. Add new columns to existing bookings table
ALTER TABLE bookings 
ADD COLUMN IF NOT EXISTS recurring_series_id VARCHAR(36) NULL,
ADD COLUMN IF NOT EXISTS is_recurring BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS has_multiple_services BOOLEAN DEFAULT FALSE;

-- 2. Create recurring booking series table
CREATE TABLE IF NOT EXISTS recurring_booking_series (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36),
    package_id VARCHAR(36),
    staff_id VARCHAR(36),
    title VARCHAR(255) NOT NULL,
    recurrence_type ENUM('WEEKLY', 'BI_WEEKLY', 'MONTHLY') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    max_occurrences INT,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    points_used INT DEFAULT 0,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL,
    FOREIGN KEY (staff_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 3. Create booking services junction table for multiple services per booking
CREATE TABLE IF NOT EXISTS booking_services (
    id VARCHAR(36) PRIMARY KEY,
    booking_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36) NOT NULL,
    service_price DECIMAL(10,2) NOT NULL,
    service_duration INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_booking_service (booking_id, service_id)
);

-- 4. Add foreign key constraint for recurring_series_id
ALTER TABLE bookings 
ADD CONSTRAINT fk_bookings_recurring_series 
FOREIGN KEY (recurring_series_id) REFERENCES recurring_booking_series(id) ON DELETE SET NULL;

-- 5. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_recurring_series ON bookings(recurring_series_id);
CREATE INDEX IF NOT EXISTS idx_bookings_is_recurring ON bookings(is_recurring);
CREATE INDEX IF NOT EXISTS idx_bookings_has_multiple_services ON bookings(has_multiple_services);
CREATE INDEX IF NOT EXISTS idx_recurring_series_user ON recurring_booking_series(user_id);
CREATE INDEX IF NOT EXISTS idx_recurring_series_active ON recurring_booking_series(is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_series_dates ON recurring_booking_series(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_booking_services_booking ON booking_services(booking_id);
CREATE INDEX IF NOT EXISTS idx_booking_services_service ON booking_services(service_id);

-- 6. Create a view for easy booking details with services
CREATE OR REPLACE VIEW booking_details_with_services AS
SELECT 
    b.*,
    u.name as customer_name,
    u.email as customer_email,
    u.phone as customer_phone,
    st.name as staff_name,
    st.email as staff_email,
    st.phone as staff_phone,
    s.name as service_name,
    s.duration as service_duration,
    s.price as service_price,
    p.name as package_name,
    p.price as package_price,
    rbs.title as recurring_series_title,
    rbs.recurrence_type,
    rbs.is_active as series_active,
    GROUP_CONCAT(
        CONCAT(bs_s.name, ' (', bs.service_price, ', ', bs.service_duration, ' min)')
        SEPARATOR '; '
    ) as multiple_services_details,
    COUNT(bs.id) as services_count
FROM bookings b
LEFT JOIN users u ON b.user_id = u.id
LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
LEFT JOIN services s ON b.service_id = s.id
LEFT JOIN packages p ON b.package_id = p.id
LEFT JOIN recurring_booking_series rbs ON b.recurring_series_id = rbs.id
LEFT JOIN booking_services bs ON b.id = bs.booking_id
LEFT JOIN services bs_s ON bs.service_id = bs_s.id
GROUP BY b.id;

-- 7. Create a procedure to generate recurring bookings
DELIMITER //
CREATE OR REPLACE PROCEDURE GenerateRecurringBookings(IN series_id VARCHAR(36))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_user_id VARCHAR(36);
    DECLARE v_service_id VARCHAR(36);
    DECLARE v_package_id VARCHAR(36);
    DECLARE v_staff_id VARCHAR(36);
    DECLARE v_recurrence_type VARCHAR(20);
    DECLARE v_start_date DATE;
    DECLARE v_end_date DATE;
    DECLARE v_max_occurrences INT;
    DECLARE v_start_time TIME;
    DECLARE v_end_time TIME;
    DECLARE v_total_amount DECIMAL(10,2);
    DECLARE v_points_used INT;
    DECLARE v_notes TEXT;
    
    DECLARE current_date DATE;
    DECLARE occurrence_count INT DEFAULT 0;
    DECLARE booking_id VARCHAR(36);
    
    -- Get series details
    SELECT user_id, service_id, package_id, staff_id, recurrence_type, start_date, end_date, 
           max_occurrences, start_time, end_time, total_amount, points_used, notes
    INTO v_user_id, v_service_id, v_package_id, v_staff_id, v_recurrence_type, v_start_date, 
         v_end_date, v_max_occurrences, v_start_time, v_end_time, v_total_amount, v_points_used, v_notes
    FROM recurring_booking_series 
    WHERE id = series_id AND is_active = 1;
    
    SET current_date = v_start_date;
    
    -- Generate bookings based on recurrence pattern
    WHILE (v_end_date IS NULL OR current_date <= v_end_date) 
          AND (v_max_occurrences IS NULL OR occurrence_count < v_max_occurrences)
          AND current_date >= CURDATE() DO
        
        -- Check if booking already exists for this date
        IF NOT EXISTS (
            SELECT 1 FROM bookings 
            WHERE recurring_series_id = series_id AND date = current_date
        ) THEN
            -- Check staff availability
            IF NOT EXISTS (
                SELECT 1 FROM bookings 
                WHERE staff_id = v_staff_id 
                AND date = current_date 
                AND status IN ('CONFIRMED', 'PENDING', 'IN_PROGRESS')
                AND (
                    (start_time <= v_start_time AND end_time > v_start_time) OR
                    (start_time < v_end_time AND end_time >= v_end_time) OR
                    (start_time >= v_start_time AND end_time <= v_end_time)
                )
            ) THEN
                -- Generate UUID for booking
                SET booking_id = UUID();
                
                -- Calculate points earned
                SET @points_earned = GREATEST(0, FLOOR((v_total_amount - (v_points_used * 10)) / 1000));
                
                -- Create booking
                INSERT INTO bookings (
                    id, user_id, service_id, package_id, staff_id, recurring_series_id,
                    date, start_time, end_time, total_amount, points_used, points_earned,
                    is_recurring, notes, status, created_at, updated_at
                ) VALUES (
                    booking_id, v_user_id, v_service_id, v_package_id, v_staff_id, series_id,
                    current_date, v_start_time, v_end_time, v_total_amount, v_points_used, @points_earned,
                    TRUE, v_notes, 'PENDING', NOW(), NOW()
                );
                
                SET occurrence_count = occurrence_count + 1;
            END IF;
        END IF;
        
        -- Calculate next date based on recurrence type
        CASE v_recurrence_type
            WHEN 'WEEKLY' THEN SET current_date = DATE_ADD(current_date, INTERVAL 1 WEEK);
            WHEN 'BI_WEEKLY' THEN SET current_date = DATE_ADD(current_date, INTERVAL 2 WEEK);
            WHEN 'MONTHLY' THEN SET current_date = DATE_ADD(current_date, INTERVAL 1 MONTH);
        END CASE;
        
        -- Safety check to prevent infinite loops
        IF occurrence_count > 100 THEN
            LEAVE;
        END IF;
    END WHILE;
    
    SELECT occurrence_count as bookings_generated;
END //
DELIMITER ;

-- 8. Create a procedure to clean up expired recurring series
DELIMITER //
CREATE OR REPLACE PROCEDURE CleanupExpiredRecurringSeries()
BEGIN
    -- Deactivate series that have passed their end date
    UPDATE recurring_booking_series 
    SET is_active = FALSE, updated_at = NOW()
    WHERE is_active = TRUE 
    AND end_date IS NOT NULL 
    AND end_date < CURDATE();
    
    -- Deactivate series that have reached their max occurrences
    UPDATE recurring_booking_series rbs
    SET is_active = FALSE, updated_at = NOW()
    WHERE is_active = TRUE 
    AND max_occurrences IS NOT NULL
    AND (
        SELECT COUNT(*) 
        FROM bookings b 
        WHERE b.recurring_series_id = rbs.id 
        AND b.status NOT IN ('CANCELLED', 'NO_SHOW')
    ) >= max_occurrences;
    
    SELECT ROW_COUNT() as series_deactivated;
END //
DELIMITER ;

-- 9. Create triggers to maintain data consistency
DELIMITER //
CREATE OR REPLACE TRIGGER tr_booking_services_update_booking
AFTER INSERT ON booking_services
FOR EACH ROW
BEGIN
    -- Update booking to mark it as having multiple services
    UPDATE bookings 
    SET has_multiple_services = TRUE, updated_at = NOW()
    WHERE id = NEW.booking_id;
END //
DELIMITER ;

-- 10. Insert sample data for testing (optional - remove in production)
-- INSERT INTO recurring_booking_series (
--     id, user_id, service_id, staff_id, title, recurrence_type, start_date, end_date,
--     start_time, end_time, total_amount, points_used, notes
-- ) VALUES (
--     UUID(), 
--     (SELECT id FROM users WHERE role = 'CUSTOMER' LIMIT 1),
--     (SELECT id FROM services WHERE is_active = 1 LIMIT 1),
--     (SELECT id FROM users WHERE role = 'STAFF' LIMIT 1),
--     'Weekly Hair Maintenance',
--     'WEEKLY',
--     CURDATE(),
--     DATE_ADD(CURDATE(), INTERVAL 3 MONTH),
--     '10:00:00',
--     '11:00:00',
--     50000,
--     0,
--     'Regular weekly hair maintenance appointment'
-- );

-- Verify the changes
SELECT 'Booking system enhancements applied successfully!' as status;
SHOW TABLES LIKE '%booking%';
DESCRIBE bookings;
DESCRIBE recurring_booking_series;
DESCRIBE booking_services;
