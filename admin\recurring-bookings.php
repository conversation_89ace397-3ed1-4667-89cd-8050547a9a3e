<?php
/**
 * Admin Recurring Bookings Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/recurring_booking_functions.php';

// Check if user is admin
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    redirect('/auth/login.php');
}

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'generate_bookings':
            $seriesId = $_POST['series_id'] ?? '';
            if ($seriesId) {
                $result = generateRecurringBookings($seriesId);
                if ($result['success']) {
                    $_SESSION['success'] = "Generated {$result['count']} new bookings for the series.";
                } else {
                    $_SESSION['error'] = $result['error'];
                }
            }
            break;
            
        case 'deactivate_series':
            $seriesId = $_POST['series_id'] ?? '';
            if ($seriesId) {
                $result = cancelRecurringBookingSeries($seriesId, false);
                if ($result['success']) {
                    $_SESSION['success'] = 'Recurring series deactivated successfully.';
                } else {
                    $_SESSION['error'] = $result['error'];
                }
            }
            break;
    }
    
    redirect('/admin/recurring-bookings.php');
}

// Get all recurring booking series with pagination
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;
$search = sanitize($_GET['search'] ?? '');
$status = $_GET['status'] ?? '';

$whereClause = "WHERE 1=1";
$params = [];

if ($search) {
    $whereClause .= " AND (rbs.title LIKE ? OR u.name LIKE ? OR u.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status === 'active') {
    $whereClause .= " AND rbs.is_active = 1";
} elseif ($status === 'inactive') {
    $whereClause .= " AND rbs.is_active = 0";
}

$recurringSeries = $database->fetchAll(
    "SELECT rbs.*, 
            u.name as customer_name, u.email as customer_email,
            s.name as service_name, s.duration as service_duration,
            p.name as package_name,
            st.name as staff_name,
            COUNT(b.id) as total_bookings,
            COUNT(CASE WHEN b.status = 'COMPLETED' THEN 1 END) as completed_bookings,
            COUNT(CASE WHEN b.status IN ('PENDING', 'CONFIRMED') THEN 1 END) as upcoming_bookings
     FROM recurring_booking_series rbs
     LEFT JOIN users u ON rbs.user_id = u.id
     LEFT JOIN services s ON rbs.service_id = s.id
     LEFT JOIN packages p ON rbs.package_id = p.id
     LEFT JOIN users st ON rbs.staff_id = st.id AND st.role = 'STAFF'
     LEFT JOIN bookings b ON rbs.id = b.recurring_series_id
     $whereClause
     GROUP BY rbs.id
     ORDER BY rbs.created_at DESC
     LIMIT $limit OFFSET $offset",
    $params
);

$totalSeries = $database->fetch(
    "SELECT COUNT(DISTINCT rbs.id) as count 
     FROM recurring_booking_series rbs
     LEFT JOIN users u ON rbs.user_id = u.id
     $whereClause",
    $params
)['count'];

$totalPages = ceil($totalSeries / $limit);

$pageTitle = 'Recurring Bookings Management';
require_once __DIR__ . '/../includes/admin_header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
    <div class="flex">
        <!-- Sidebar -->
        <?php require_once __DIR__ . '/../includes/admin_sidebar.php'; ?>

        <!-- Main Content -->
        <div class="flex-1 lg:ml-64">
            <main class="p-6">
                <!-- Header -->
                <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 shadow-xl rounded-2xl p-8 mb-8 hover-lift">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <div>
                            <h1 class="text-3xl font-bold text-white font-serif">Recurring <span class="text-salon-gold">Bookings</span></h1>
                            <p class="mt-2 text-lg text-gray-300">Manage recurring appointment series</p>
                        </div>
                        <div class="mt-6 sm:mt-0 flex gap-4">
                            <button onclick="cleanupExpiredSeries()" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-broom mr-2"></i>Cleanup Expired
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Messages -->
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="mb-6 p-4 rounded-lg bg-green-100 border border-green-400 text-green-700">
                        <?= htmlspecialchars($_SESSION['success']) ?>
                    </div>
                    <?php unset($_SESSION['success']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="mb-6 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700">
                        <?= htmlspecialchars($_SESSION['error']) ?>
                    </div>
                    <?php unset($_SESSION['error']); ?>
                <?php endif; ?>

                <!-- Filters -->
                <div class="bg-secondary-800 shadow rounded-lg p-6 mb-6">
                    <form method="GET" class="flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="Search by title, customer name, or email..." 
                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                        </div>
                        <div>
                            <select name="status" class="px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                <option value="">All Status</option>
                                <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                                <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Filter
                        </button>
                        <?php if ($search || $status): ?>
                            <a href="<?= getBasePath() ?>/admin/recurring-bookings.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                Clear
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- Recurring Series Table -->
                <div class="bg-secondary-800 shadow rounded-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-secondary-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Series</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Service/Package</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Staff</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Schedule</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Bookings</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-secondary-800 divide-y divide-secondary-700">
                                <?php if (empty($recurringSeries)): ?>
                                    <tr>
                                        <td colspan="8" class="px-6 py-12 text-center text-gray-400">
                                            <i class="fas fa-calendar-alt text-4xl mb-4"></i>
                                            <p class="text-lg">No recurring booking series found.</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($recurringSeries as $series): ?>
                                        <tr class="hover:bg-secondary-700">
                                            <td class="px-6 py-4">
                                                <div class="text-sm font-medium text-white"><?= htmlspecialchars($series['title']) ?></div>
                                                <div class="text-sm text-gray-400">
                                                    <?= ucfirst(strtolower(str_replace('_', ' ', $series['recurrence_type']))) ?>
                                                </div>
                                                <div class="text-sm text-salon-gold font-semibold"><?= formatCurrency($series['total_amount']) ?></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm font-medium text-white"><?= htmlspecialchars($series['customer_name']) ?></div>
                                                <div class="text-sm text-gray-400"><?= htmlspecialchars($series['customer_email']) ?></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-white">
                                                    <?= htmlspecialchars($series['service_name'] ?: $series['package_name']) ?>
                                                </div>
                                                <?php if ($series['service_duration']): ?>
                                                    <div class="text-sm text-gray-400"><?= $series['service_duration'] ?> minutes</div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-white"><?= htmlspecialchars($series['staff_name']) ?></div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-white">
                                                    <?= date('M j, Y', strtotime($series['start_date'])) ?>
                                                    <?php if ($series['end_date']): ?>
                                                        - <?= date('M j, Y', strtotime($series['end_date'])) ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-sm text-gray-400">
                                                    <?= date('g:i A', strtotime($series['start_time'])) ?> - 
                                                    <?= date('g:i A', strtotime($series['end_time'])) ?>
                                                </div>
                                                <?php if ($series['max_occurrences']): ?>
                                                    <div class="text-sm text-gray-400">Max: <?= $series['max_occurrences'] ?> appointments</div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm">
                                                    <span class="text-white"><?= $series['total_bookings'] ?></span> total
                                                </div>
                                                <div class="text-sm">
                                                    <span class="text-green-400"><?= $series['completed_bookings'] ?></span> completed
                                                </div>
                                                <div class="text-sm">
                                                    <span class="text-blue-400"><?= $series['upcoming_bookings'] ?></span> upcoming
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $series['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                                    <?= $series['is_active'] ? 'Active' : 'Inactive' ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex gap-2">
                                                    <a href="<?= getBasePath() ?>/admin/bookings?series_id=<?= $series['id'] ?>" 
                                                       class="text-blue-400 hover:text-blue-300 text-sm font-medium">
                                                        View Bookings
                                                    </a>
                                                    <?php if ($series['is_active']): ?>
                                                        <button onclick="generateBookings('<?= $series['id'] ?>')" 
                                                                class="text-green-400 hover:text-green-300 text-sm font-medium">
                                                            Generate
                                                        </button>
                                                        <button onclick="deactivateSeries('<?= $series['id'] ?>', '<?= htmlspecialchars($series['title']) ?>')" 
                                                                class="text-red-400 hover:text-red-300 text-sm font-medium">
                                                            Deactivate
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="bg-secondary-800 px-4 py-3 flex items-center justify-between border-t border-secondary-700 sm:px-6 rounded-lg mt-6">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                   class="relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Previous
                                </a>
                            <?php endif; ?>
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-secondary-600 text-sm font-medium rounded-md text-gray-300 bg-secondary-700 hover:bg-secondary-600">
                                    Next
                                </a>
                            <?php endif; ?>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-300">
                                    Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $limit, $totalSeries) ?></span> of <span class="font-medium"><?= $totalSeries ?></span> results
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>" 
                                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $page ? 'z-10 bg-salon-gold border-salon-gold text-black' : 'bg-secondary-700 border-secondary-600 text-gray-300 hover:bg-secondary-600' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>
                                </nav>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
</div>

<script>
function generateBookings(seriesId) {
    if (confirm('Generate new bookings for this recurring series?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="generate_bookings">
            <input type="hidden" name="series_id" value="${seriesId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function deactivateSeries(seriesId, title) {
    if (confirm(`Are you sure you want to deactivate the recurring series "${title}"?`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="deactivate_series">
            <input type="hidden" name="series_id" value="${seriesId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function cleanupExpiredSeries() {
    if (confirm('Clean up expired recurring series? This will deactivate series that have passed their end date or reached their maximum occurrences.')) {
        fetch('<?= getBasePath() ?>/api/admin/recurring-bookings.php?action=cleanup', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Cleaned up ${data.deactivated} expired series.`);
                location.reload();
            } else {
                alert('Error cleaning up expired series: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error cleaning up expired series.');
        });
    }
}
</script>

<?php require_once __DIR__ . '/../includes/admin_footer.php'; ?>
