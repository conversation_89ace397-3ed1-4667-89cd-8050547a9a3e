<?php
/**
 * Customer Recurring Bookings Management
 * Flix Salonce - PHP Version
 */

require_once __DIR__ . '/../config/app.php';
require_once __DIR__ . '/../includes/recurring_booking_functions.php';

// Check if user is logged in and is a customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

$userId = $_SESSION['user_id'];

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'cancel_series':
            $seriesId = $_POST['series_id'] ?? '';
            $cancelFuture = isset($_POST['cancel_future']);
            
            if ($seriesId) {
                $result = cancelRecurringBookingSeries($seriesId, $cancelFuture);
                if ($result['success']) {
                    $_SESSION['success'] = $result['message'];
                } else {
                    $_SESSION['error'] = $result['error'];
                }
            }
            break;
    }
    
    redirect('/customer/recurring-bookings.php');
}

// Get user's recurring booking series
$recurringSeries = getUserRecurringBookingSeries($userId, true);

// Get user profile for points display
$profile = $database->fetch("SELECT * FROM users WHERE id = ?", [$userId]);

$pageTitle = 'Recurring Bookings';
require_once __DIR__ . '/../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
    <!-- Navigation -->
    <?php require_once __DIR__ . '/../includes/customer_nav.php'; ?>

    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 shadow-xl rounded-2xl p-8 mb-8 hover-lift">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white font-serif">Recurring <span class="text-salon-gold">Bookings</span></h1>
                    <p class="mt-2 text-lg text-gray-300">Manage your recurring appointment series</p>
                </div>
                <div class="mt-6 sm:mt-0">
                    <a href="<?= getBasePath() ?>/customer/book" 
                       class="inline-flex items-center px-6 py-3 bg-salon-gold text-black rounded-xl font-semibold hover:bg-gold-light transition-all duration-300 hover:scale-105 shadow-lg">
                        <i class="fas fa-plus mr-2"></i>Create New Recurring Booking
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="mb-6 p-4 rounded-lg bg-green-100 border border-green-400 text-green-700">
                <?= htmlspecialchars($_SESSION['success']) ?>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-6 p-4 rounded-lg bg-red-100 border border-red-400 text-red-700">
                <?= htmlspecialchars($_SESSION['error']) ?>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <!-- Recurring Series List -->
        <?php if (empty($recurringSeries)): ?>
            <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-12 text-center">
                <div class="text-gray-400 text-lg mb-6">
                    <i class="fas fa-calendar-alt text-6xl mb-4"></i>
                    <p class="text-xl font-semibold mb-2">No Recurring Bookings</p>
                    <p>You haven't set up any recurring appointments yet.</p>
                </div>
                <a href="<?= getBasePath() ?>/customer/book" 
                   class="inline-flex items-center px-6 py-3 bg-salon-gold text-black rounded-xl font-semibold hover:bg-gold-light transition-all duration-300 hover:scale-105 shadow-lg">
                    <i class="fas fa-plus mr-2"></i>Create Your First Recurring Booking
                </a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <?php foreach ($recurringSeries as $series): ?>
                    <div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl p-6 hover-lift">
                        <!-- Series Header -->
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-xl font-bold text-white font-serif"><?= htmlspecialchars($series['title']) ?></h3>
                                <div class="flex items-center mt-2">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?= $series['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                        <?= $series['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                    <span class="ml-3 text-sm text-gray-400">
                                        <?= ucfirst(strtolower(str_replace('_', ' ', $series['recurrence_type']))) ?>
                                    </span>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-salon-gold"><?= formatCurrency($series['total_amount']) ?></div>
                                <div class="text-sm text-gray-400">per appointment</div>
                            </div>
                        </div>

                        <!-- Service/Package Info -->
                        <div class="mb-4">
                            <?php if ($series['service_name']): ?>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-cut mr-2 text-salon-gold"></i>
                                    <span><?= htmlspecialchars($series['service_name']) ?></span>
                                    <span class="ml-2 text-sm text-gray-400">(<?= $series['service_duration'] ?> min)</span>
                                </div>
                            <?php elseif ($series['package_name']): ?>
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-box mr-2 text-salon-gold"></i>
                                    <span><?= htmlspecialchars($series['package_name']) ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <div class="flex items-center text-gray-300 mt-2">
                                <i class="fas fa-user mr-2 text-salon-gold"></i>
                                <span><?= htmlspecialchars($series['staff_name']) ?></span>
                            </div>
                        </div>

                        <!-- Schedule Info -->
                        <div class="mb-4 p-4 bg-secondary-800/50 rounded-lg">
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-400">Start Date:</span>
                                    <div class="text-white font-medium"><?= date('M j, Y', strtotime($series['start_date'])) ?></div>
                                </div>
                                <div>
                                    <span class="text-gray-400">Time:</span>
                                    <div class="text-white font-medium">
                                        <?= date('g:i A', strtotime($series['start_time'])) ?> - 
                                        <?= date('g:i A', strtotime($series['end_time'])) ?>
                                    </div>
                                </div>
                                <?php if ($series['end_date']): ?>
                                    <div>
                                        <span class="text-gray-400">End Date:</span>
                                        <div class="text-white font-medium"><?= date('M j, Y', strtotime($series['end_date'])) ?></div>
                                    </div>
                                <?php endif; ?>
                                <?php if ($series['max_occurrences']): ?>
                                    <div>
                                        <span class="text-gray-400">Max Appointments:</span>
                                        <div class="text-white font-medium"><?= $series['max_occurrences'] ?></div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Statistics -->
                        <div class="mb-4 grid grid-cols-3 gap-4 text-center">
                            <div class="p-3 bg-secondary-800/50 rounded-lg">
                                <div class="text-lg font-bold text-salon-gold"><?= $series['total_bookings'] ?></div>
                                <div class="text-xs text-gray-400">Total</div>
                            </div>
                            <div class="p-3 bg-secondary-800/50 rounded-lg">
                                <div class="text-lg font-bold text-green-400"><?= $series['completed_bookings'] ?></div>
                                <div class="text-xs text-gray-400">Completed</div>
                            </div>
                            <div class="p-3 bg-secondary-800/50 rounded-lg">
                                <div class="text-lg font-bold text-blue-400"><?= $series['upcoming_bookings'] ?></div>
                                <div class="text-xs text-gray-400">Upcoming</div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <?php if ($series['notes']): ?>
                            <div class="mb-4 p-3 bg-secondary-800/50 rounded-lg">
                                <div class="text-sm text-gray-400 mb-1">Notes:</div>
                                <div class="text-white text-sm"><?= htmlspecialchars($series['notes']) ?></div>
                            </div>
                        <?php endif; ?>

                        <!-- Actions -->
                        <div class="flex gap-3">
                            <a href="<?= getBasePath() ?>/customer/bookings?series_id=<?= $series['id'] ?>" 
                               class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors text-center">
                                <i class="fas fa-list mr-2"></i>View Bookings
                            </a>
                            
                            <?php if ($series['is_active']): ?>
                                <button onclick="cancelSeries('<?= $series['id'] ?>', '<?= htmlspecialchars($series['title']) ?>')" 
                                        class="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors">
                                    <i class="fas fa-stop mr-2"></i>Cancel Series
                                </button>
                            <?php else: ?>
                                <span class="flex-1 bg-gray-600 text-gray-300 py-2 px-4 rounded-lg text-sm font-medium text-center cursor-not-allowed">
                                    <i class="fas fa-ban mr-2"></i>Cancelled
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Cancel Series Modal -->
<div id="cancelModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-secondary-800 rounded-lg p-6 w-full max-w-md mx-4 border border-red-600">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0 mr-3">
                <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-white">Cancel Recurring Series</h3>
        </div>
        
        <div class="text-gray-300 mb-6">
            <p id="cancelMessage" class="mb-4"></p>
            <div class="bg-secondary-700 p-3 rounded-lg">
                <label class="flex items-center cursor-pointer">
                    <input type="checkbox" id="cancelFutureBookings" checked 
                           class="text-red-500 focus:ring-red-500 focus:ring-offset-secondary-800">
                    <span class="ml-2 text-sm">Also cancel all future appointments in this series</span>
                </label>
            </div>
        </div>
        
        <div class="flex gap-3 justify-end">
            <button id="cancelModalCancel" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                Keep Series
            </button>
            <button id="cancelModalConfirm" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-semibold">
                Cancel Series
            </button>
        </div>
    </div>
</div>

<script>
let currentSeriesId = null;

function cancelSeries(seriesId, seriesTitle) {
    currentSeriesId = seriesId;
    document.getElementById('cancelMessage').textContent = `Are you sure you want to cancel the recurring series "${seriesTitle}"?`;
    document.getElementById('cancelModal').classList.remove('hidden');
}

document.getElementById('cancelModalCancel').addEventListener('click', function() {
    document.getElementById('cancelModal').classList.add('hidden');
    currentSeriesId = null;
});

document.getElementById('cancelModalConfirm').addEventListener('click', function() {
    if (currentSeriesId) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="cancel_series">
            <input type="hidden" name="series_id" value="${currentSeriesId}">
            ${document.getElementById('cancelFutureBookings').checked ? '<input type="hidden" name="cancel_future" value="1">' : ''}
        `;
        document.body.appendChild(form);
        form.submit();
    }
});

// Close modal when clicking outside
document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
        currentSeriesId = null;
    }
});
</script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
