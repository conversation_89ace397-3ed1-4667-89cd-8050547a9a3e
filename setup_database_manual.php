<?php
/**
 * Manual Database Setup for Booking Enhancements
 * Run this script to manually set up the database changes
 */

require_once __DIR__ . '/config/app.php';

// Only allow admin access
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    die('This setup script can only be run by administrators.');
}

echo "<h1>Manual Database Setup</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
    .success { color: #4ade80; }
    .error { color: #f87171; }
    .warning { color: #fbbf24; }
    .info { color: #60a5fa; }
    pre { background: #2a2a2a; padding: 15px; border-radius: 5px; overflow-x: auto; }
</style>\n";

if ($_POST['action'] ?? '' === 'execute') {
    echo "<h2>Executing Database Changes...</h2>\n";
    
    $steps = [
        "Add recurring_series_id column to bookings" => "ALTER TABLE bookings ADD COLUMN recurring_series_id VARCHAR(36) NULL",
        "Add is_recurring column to bookings" => "ALTER TABLE bookings ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE",
        "Add has_multiple_services column to bookings" => "ALTER TABLE bookings ADD COLUMN has_multiple_services BOOLEAN DEFAULT FALSE",
        "Create recurring_booking_series table" => "CREATE TABLE recurring_booking_series (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) NOT NULL,
            service_id VARCHAR(36),
            package_id VARCHAR(36),
            staff_id VARCHAR(36),
            title VARCHAR(255) NOT NULL,
            recurrence_type ENUM('WEEKLY', 'BI_WEEKLY', 'MONTHLY') NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE,
            max_occurrences INT,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            points_used INT DEFAULT 0,
            notes TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL,
            FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL,
            FOREIGN KEY (staff_id) REFERENCES users(id) ON DELETE SET NULL
        )",
        "Create booking_services table" => "CREATE TABLE booking_services (
            id VARCHAR(36) PRIMARY KEY,
            booking_id VARCHAR(36) NOT NULL,
            service_id VARCHAR(36) NOT NULL,
            service_price DECIMAL(10,2) NOT NULL,
            service_duration INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
            FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
            UNIQUE KEY unique_booking_service (booking_id, service_id)
        )"
    ];
    
    $indexes = [
        "Index on bookings.recurring_series_id" => "CREATE INDEX idx_bookings_recurring_series ON bookings(recurring_series_id)",
        "Index on bookings.is_recurring" => "CREATE INDEX idx_bookings_is_recurring ON bookings(is_recurring)",
        "Index on bookings.has_multiple_services" => "CREATE INDEX idx_bookings_has_multiple_services ON bookings(has_multiple_services)",
        "Index on recurring_booking_series.user_id" => "CREATE INDEX idx_recurring_series_user ON recurring_booking_series(user_id)",
        "Index on recurring_booking_series.is_active" => "CREATE INDEX idx_recurring_series_active ON recurring_booking_series(is_active)",
        "Index on booking_services.booking_id" => "CREATE INDEX idx_booking_services_booking ON booking_services(booking_id)",
        "Index on booking_services.service_id" => "CREATE INDEX idx_booking_services_service ON booking_services(service_id)"
    ];
    
    $foreignKeys = [
        "Foreign key for bookings.recurring_series_id" => "ALTER TABLE bookings ADD CONSTRAINT fk_bookings_recurring_series FOREIGN KEY (recurring_series_id) REFERENCES recurring_booking_series(id) ON DELETE SET NULL"
    ];
    
    $successCount = 0;
    $totalSteps = count($steps) + count($indexes) + count($foreignKeys);
    
    // Execute main table changes
    echo "<h3>Creating Tables and Columns</h3>\n";
    foreach ($steps as $stepName => $sql) {
        try {
            $database->query($sql);
            echo "<p class='success'>✓ $stepName</p>\n";
            $successCount++;
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false || 
                strpos($e->getMessage(), 'already exists') !== false) {
                echo "<p class='warning'>⚠ $stepName (already exists)</p>\n";
                $successCount++;
            } else {
                echo "<p class='error'>✗ $stepName: " . $e->getMessage() . "</p>\n";
            }
        }
    }
    
    // Execute foreign keys
    echo "<h3>Adding Foreign Key Constraints</h3>\n";
    foreach ($foreignKeys as $stepName => $sql) {
        try {
            $database->query($sql);
            echo "<p class='success'>✓ $stepName</p>\n";
            $successCount++;
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate foreign key constraint name') !== false) {
                echo "<p class='warning'>⚠ $stepName (already exists)</p>\n";
                $successCount++;
            } else {
                echo "<p class='error'>✗ $stepName: " . $e->getMessage() . "</p>\n";
            }
        }
    }
    
    // Execute indexes
    echo "<h3>Creating Indexes</h3>\n";
    foreach ($indexes as $stepName => $sql) {
        try {
            $database->query($sql);
            echo "<p class='success'>✓ $stepName</p>\n";
            $successCount++;
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p class='warning'>⚠ $stepName (already exists)</p>\n";
                $successCount++;
            } else {
                echo "<p class='error'>✗ $stepName: " . $e->getMessage() . "</p>\n";
            }
        }
    }
    
    echo "<h3>Setup Complete</h3>\n";
    echo "<p class='info'>Successfully completed $successCount out of $totalSteps steps.</p>\n";
    
    if ($successCount >= $totalSteps - 2) { // Allow for a couple of minor errors
        echo "<p class='success'><strong>✓ Database setup appears to be successful!</strong></p>\n";
        echo "<p><a href='" . getBasePath() . "/test_booking_enhancements.php'>→ Run System Tests</a></p>\n";
        echo "<p><a href='" . getBasePath() . "/admin'>→ Go to Admin Dashboard</a></p>\n";
    } else {
        echo "<p class='error'><strong>⚠ Some steps failed. Please check the errors above.</strong></p>\n";
    }
    
} else {
    echo "<h2>Ready to Set Up Database</h2>\n";
    echo "<p class='info'>This script will create the necessary database tables and columns for the booking enhancements.</p>\n";
    
    echo "<h3>What will be created:</h3>\n";
    echo "<ul>\n";
    echo "<li>New columns in <code>bookings</code> table: recurring_series_id, is_recurring, has_multiple_services</li>\n";
    echo "<li>New table: <code>recurring_booking_series</code></li>\n";
    echo "<li>New table: <code>booking_services</code></li>\n";
    echo "<li>Database indexes for performance</li>\n";
    echo "<li>Foreign key constraints</li>\n";
    echo "</ul>\n";
    
    echo "<h3>Current Database Status:</h3>\n";
    
    // Check current status
    try {
        $bookingColumns = $database->fetchAll("DESCRIBE bookings");
        $existingColumns = array_column($bookingColumns, 'Field');
        
        $requiredColumns = ['recurring_series_id', 'is_recurring', 'has_multiple_services'];
        foreach ($requiredColumns as $column) {
            if (in_array($column, $existingColumns)) {
                echo "<p class='success'>✓ Column bookings.$column already exists</p>\n";
            } else {
                echo "<p class='warning'>⚠ Column bookings.$column needs to be created</p>\n";
            }
        }
        
        $tables = ['recurring_booking_series', 'booking_services'];
        foreach ($tables as $table) {
            $result = $database->fetch("SHOW TABLES LIKE '$table'");
            if ($result) {
                echo "<p class='success'>✓ Table $table already exists</p>\n";
            } else {
                echo "<p class='warning'>⚠ Table $table needs to be created</p>\n";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>Error checking database status: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<form method='POST'>\n";
    echo "<input type='hidden' name='action' value='execute'>\n";
    echo "<button type='submit' style='background: #fbbf24; color: #000; padding: 10px 20px; border: none; border-radius: 5px; font-weight: bold; cursor: pointer;'>Execute Database Setup</button>\n";
    echo "</form>\n";
}

echo "<hr style='margin: 30px 0; border-color: #444;'>\n";
echo "<h3>Alternative: Manual SQL Execution</h3>\n";
echo "<p class='info'>If the automatic setup doesn't work, you can manually execute these SQL commands in your database:</p>\n";

echo "<pre>-- Add new columns to bookings table
ALTER TABLE bookings ADD COLUMN recurring_series_id VARCHAR(36) NULL;
ALTER TABLE bookings ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE;
ALTER TABLE bookings ADD COLUMN has_multiple_services BOOLEAN DEFAULT FALSE;

-- Create recurring_booking_series table
CREATE TABLE recurring_booking_series (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36),
    package_id VARCHAR(36),
    staff_id VARCHAR(36),
    title VARCHAR(255) NOT NULL,
    recurrence_type ENUM('WEEKLY', 'BI_WEEKLY', 'MONTHLY') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    max_occurrences INT,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    points_used INT DEFAULT 0,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL,
    FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL,
    FOREIGN KEY (staff_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create booking_services table
CREATE TABLE booking_services (
    id VARCHAR(36) PRIMARY KEY,
    booking_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36) NOT NULL,
    service_price DECIMAL(10,2) NOT NULL,
    service_duration INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_booking_service (booking_id, service_id)
);

-- Add foreign key constraint
ALTER TABLE bookings ADD CONSTRAINT fk_bookings_recurring_series 
FOREIGN KEY (recurring_series_id) REFERENCES recurring_booking_series(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX idx_bookings_recurring_series ON bookings(recurring_series_id);
CREATE INDEX idx_bookings_is_recurring ON bookings(is_recurring);
CREATE INDEX idx_bookings_has_multiple_services ON bookings(has_multiple_services);
CREATE INDEX idx_recurring_series_user ON recurring_booking_series(user_id);
CREATE INDEX idx_recurring_series_active ON recurring_booking_series(is_active);
CREATE INDEX idx_booking_services_booking ON booking_services(booking_id);
CREATE INDEX idx_booking_services_service ON booking_services(service_id);</pre>\n";

echo "<p><a href='" . getBasePath() . "/admin'>← Back to Admin Dashboard</a></p>\n";
?>
