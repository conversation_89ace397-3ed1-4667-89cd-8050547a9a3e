<?php
/**
 * Customer Multiple Services API
 * Flix Salonce - PHP Version
 */

header('Content-Type: application/json');
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/multiple_services_functions.php';

// Check if user is logged in and is a customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$userId = $_SESSION['user_id'];

try {
    switch ($method) {
        case 'POST':
            $action = $_GET['action'] ?? 'create';
            
            switch ($action) {
                case 'create':
                    handleCreateMultipleServicesBooking($userId);
                    break;
                    
                case 'calculate':
                    handleCalculateMultipleServices();
                    break;
                    
                case 'check_availability':
                    handleCheckAvailabilityForMultipleServices();
                    break;
                    
                case 'get_time_slots':
                    handleGetTimeSlotsForMultipleServices();
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode(['error' => 'Invalid action']);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleCreateMultipleServicesBooking($userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit;
    }
    
    // Validate required fields
    $requiredFields = ['services', 'staff_id', 'date', 'start_time'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            exit;
        }
    }
    
    // Validate services array
    if (!is_array($input['services']) || count($input['services']) < 1) {
        http_response_code(400);
        echo json_encode(['error' => 'At least one service must be selected']);
        exit;
    }
    
    $result = createBookingWithMultipleServices($userId, $input);
    
    if ($result['success']) {
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
}

function handleCalculateMultipleServices() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['services']) || !is_array($input['services'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Services array is required']);
        exit;
    }
    
    $totals = calculateMultipleServicesTotal($input['services']);
    
    // Apply points discount if specified
    $pointsUsed = intval($input['points_used'] ?? 0);
    $discount = $pointsUsed * 10; // 1 point = TSH 10
    $finalAmount = max(0, $totals['total_price'] - $discount);
    
    // Calculate points earned (1 point per TSH 1,000 spent)
    $pointsEarned = max(0, floor($finalAmount / 1000));
    
    echo json_encode([
        'success' => true,
        'total_price' => $totals['total_price'],
        'total_duration' => $totals['total_duration'],
        'points_discount' => $discount,
        'final_amount' => $finalAmount,
        'points_earned' => $pointsEarned,
        'services' => $totals['services']
    ]);
}

function handleCheckAvailabilityForMultipleServices() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit;
    }
    
    // Validate required fields
    $requiredFields = ['services', 'staff_id', 'date', 'start_time'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            exit;
        }
    }
    
    $isAvailable = checkStaffAvailabilityForMultipleServices(
        $input['staff_id'],
        $input['date'],
        $input['start_time'],
        $input['services']
    );
    
    if ($isAvailable) {
        // Calculate end time for display
        $totals = calculateMultipleServicesTotal($input['services']);
        $startTime = new DateTime($input['start_time']);
        $endTime = clone $startTime;
        $endTime->add(new DateInterval("PT{$totals['total_duration']}M"));
        
        echo json_encode([
            'success' => true,
            'available' => true,
            'message' => 'Staff is available for the selected services',
            'total_duration' => $totals['total_duration'],
            'end_time' => $endTime->format('H:i')
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'available' => false,
            'message' => 'Staff is not available for the required time duration'
        ]);
    }
}

function handleGetTimeSlotsForMultipleServices() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit;
    }
    
    // Validate required fields
    $requiredFields = ['services', 'staff_id', 'date'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            exit;
        }
    }
    
    // Validate date format
    if (!DateTime::createFromFormat('Y-m-d', $input['date'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid date format']);
        exit;
    }
    
    // Check if date is not in the past
    if ($input['date'] < date('Y-m-d')) {
        echo json_encode([
            'success' => false,
            'message' => 'Cannot book appointments in the past',
            'slots' => []
        ]);
        exit;
    }
    
    $slots = getAvailableTimeSlotsForMultipleServices(
        $input['date'],
        $input['services'],
        $input['staff_id']
    );
    
    echo json_encode([
        'success' => true,
        'slots' => $slots,
        'date' => $input['date'],
        'staff_id' => $input['staff_id'],
        'services_count' => count($input['services'])
    ]);
}

/**
 * Get previous bookings for rebook functionality
 */
function getPreviousBookingsForRebook($userId) {
    global $database;
    
    // Get completed bookings from the last 6 months
    $bookings = $database->fetchAll(
        "SELECT b.*, 
                s.name as service_name, s.duration as service_duration, s.price as service_price,
                p.name as package_name, p.price as package_price,
                st.name as staff_name
         FROM bookings b
         LEFT JOIN services s ON b.service_id = s.id
         LEFT JOIN packages p ON b.package_id = p.id
         LEFT JOIN users st ON b.staff_id = st.id AND st.role = 'STAFF'
         WHERE b.user_id = ? 
         AND b.status = 'COMPLETED'
         AND b.date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
         ORDER BY b.date DESC, b.start_time DESC
         LIMIT 20",
        [$userId]
    );
    
    // For each booking, get associated services if it has multiple services
    foreach ($bookings as &$booking) {
        if ($booking['has_multiple_services']) {
            $booking['services'] = getBookingServices($booking['id']);
        }
    }
    
    return $bookings;
}

// Add endpoint for getting previous bookings
if ($method === 'GET' && ($_GET['action'] ?? '') === 'previous_bookings') {
    $previousBookings = getPreviousBookingsForRebook($userId);
    
    echo json_encode([
        'success' => true,
        'bookings' => $previousBookings
    ]);
    exit;
}
