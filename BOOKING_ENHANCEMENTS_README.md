# Booking System Enhancements

This document outlines the implementation of two major enhancements to the flix-php booking system:

1. **Recurring Bookings Feature**
2. **Multiple Service Selection for Single Booking**

## Overview

These enhancements provide customers with more flexible booking options while maintaining the existing visual design standards and system architecture.

## 1. Recurring Bookings Feature

### Features Implemented

- **Configurable Intervals**: Weekly, bi-weekly, and monthly recurring appointments
- **End Conditions**: Set end dates or specify number of occurrences
- **Rebook Previous Service**: Quick recreation of past bookings with same service details
- **Staff Availability Respect**: Automatic checking of staff availability and business hours
- **Management Interface**: Staff/admin can view and modify recurring booking series

### Database Changes

#### New Tables

1. **`recurring_booking_series`**
   - Stores recurring booking configurations
   - Fields: id, user_id, service_id, package_id, staff_id, title, recurrence_type, start_date, end_date, max_occurrences, start_time, end_time, total_amount, points_used, notes, is_active

2. **Updated `bookings` table**
   - Added `recurring_series_id` (foreign key)
   - Added `is_recurring` (boolean flag)
   - Added `has_multiple_services` (boolean flag)

### API Endpoints

- **Customer API**: `/api/customer/recurring-bookings.php`
  - GET: Retrieve user's recurring series
  - POST: Create new recurring series
  - PUT: Update existing series
  - DELETE: Cancel recurring series

- **Admin API**: `/api/admin/recurring-bookings.php`
  - GET: Retrieve all recurring series with pagination
  - POST: Generate bookings, cleanup expired series
  - PUT: Update series details
  - DELETE: Deactivate series

### User Interfaces

#### Customer Interface
- **Booking Form**: Added recurring options in booking flow
- **Recurring Bookings Page**: `/customer/recurring-bookings.php`
  - View all recurring series
  - Cancel series
  - View individual bookings in series

#### Admin Interface
- **Admin Recurring Bookings**: `/admin/recurring-bookings.php`
  - Manage all recurring series
  - Generate new bookings for series
  - Deactivate expired series
  - View statistics and analytics

### Key Functions

- `createRecurringBookingSeries()`: Creates new recurring series
- `generateRecurringBookings()`: Generates individual bookings from series
- `calculateRecurringDates()`: Calculates dates based on recurrence pattern
- `cancelRecurringBookingSeries()`: Cancels series and optionally future bookings

## 2. Multiple Service Selection Feature

### Features Implemented

- **Multiple Service Selection**: Customers can select multiple services for one appointment
- **Automatic Calculations**: Total duration and pricing calculated automatically
- **Extended Scheduling**: Staff scheduling accounts for extended appointment times
- **Clear Display**: Booking confirmation shows all selected services
- **Backward Compatibility**: Maintains compatibility with existing single-service bookings

### Database Changes

#### New Tables

1. **`booking_services`**
   - Junction table for many-to-many relationship between bookings and services
   - Fields: id, booking_id, service_id, service_price, service_duration

### API Endpoints

- **Multiple Services API**: `/api/customer/multiple-services.php`
  - POST: Create booking with multiple services
  - POST (calculate): Calculate totals for selected services
  - POST (check_availability): Check staff availability for extended duration
  - POST (get_time_slots): Get available time slots for multiple services
  - GET (previous_bookings): Get previous bookings for rebook functionality

### User Interface Updates

#### Customer Booking Form
- **New Tab**: "Multiple Services" tab in service selection
- **Service Checkboxes**: Multiple selection with checkboxes
- **Live Summary**: Real-time calculation of total duration and price
- **Previous Bookings Tab**: "Rebook Previous" option for quick rebooking

### Key Functions

- `createBookingWithMultipleServices()`: Creates booking with multiple services
- `calculateMultipleServicesTotal()`: Calculates total price and duration
- `checkStaffAvailabilityForMultipleServices()`: Checks availability for extended duration
- `getAvailableTimeSlotsForMultipleServices()`: Gets available slots considering total duration
- `getBookingServices()`: Retrieves services for a booking (single or multiple)

## Installation Instructions

### 1. Database Setup

Run the database migration script:

```sql
-- Execute the following file:
source database/booking_enhancements.sql;
```

This will:
- Add new columns to existing `bookings` table
- Create `recurring_booking_series` table
- Create `booking_services` table
- Add foreign key constraints
- Create indexes for performance
- Create helpful views and stored procedures

### 2. File Structure

The following new files have been added:

```
includes/
├── recurring_booking_functions.php      # Core recurring booking logic
├── multiple_services_functions.php      # Multiple services functionality

api/
├── customer/
│   ├── recurring-bookings.php          # Customer recurring bookings API
│   └── multiple-services.php           # Multiple services API
└── admin/
    └── recurring-bookings.php          # Admin recurring bookings API

customer/
└── recurring-bookings.php              # Customer recurring bookings interface

admin/
└── recurring-bookings.php              # Admin recurring bookings interface

database/
├── booking_enhancements.sql            # Database migration script
└── migrations.sql                      # Updated with new tables
```

### 3. Updated Files

The following existing files have been modified:

- `customer/book/index.php` - Enhanced booking form
- `includes/customer_sidebar_nav.php` - Added recurring bookings link
- `includes/admin_sidebar.php` - Added recurring bookings link
- `database/migrations.sql` - Updated with new table structures

## Usage Guide

### For Customers

#### Creating Recurring Bookings

1. Go to "Book Appointment"
2. Select service(s) or package
3. Choose staff and date/time
4. Check "Make this a recurring appointment"
5. Configure recurrence pattern:
   - Frequency: Weekly, Bi-weekly, or Monthly
   - End condition: End date or number of appointments
   - Optional series title

#### Booking Multiple Services

1. Go to "Book Appointment"
2. Click "Multiple Services" tab
3. Select multiple services using checkboxes
4. Review total duration and price in summary
5. Choose staff and available time slot
6. Complete booking

#### Managing Recurring Bookings

1. Go to "Recurring Bookings" from sidebar
2. View all active and inactive series
3. See statistics (total, completed, upcoming bookings)
4. Cancel series if needed

### For Administrators

#### Managing Recurring Series

1. Go to "Recurring Bookings" from admin sidebar
2. View all customer recurring series
3. Filter by status or search by customer
4. Generate additional bookings for series
5. Deactivate expired or problematic series

#### Cleanup Operations

- Use "Cleanup Expired" button to automatically deactivate expired series
- Monitor recurring booking statistics
- View individual bookings within each series

## Technical Details

### Recurring Booking Logic

- **Date Calculation**: Uses PHP DateTime with intervals for accurate date calculation
- **Availability Checking**: Integrates with existing staff availability system
- **Conflict Resolution**: Skips dates where staff is unavailable
- **Safety Limits**: Maximum 100 occurrences to prevent infinite loops

### Multiple Services Logic

- **Duration Calculation**: Sums individual service durations
- **Price Calculation**: Sums individual service prices
- **Availability Checking**: Checks staff availability for total duration
- **Time Slot Generation**: Considers extended duration when generating available slots

### Performance Considerations

- **Indexes**: Added database indexes for recurring series and booking services
- **Batch Operations**: Efficient bulk creation of recurring bookings
- **Caching**: Leverages existing caching mechanisms for service data

## Compatibility

- **Backward Compatible**: All existing bookings continue to work
- **Visual Consistency**: Maintains black color palette and glass header effects
- **Mobile Responsive**: All new interfaces are mobile-friendly
- **API Consistent**: Follows existing API patterns and authentication

## Testing Recommendations

1. **Recurring Bookings**
   - Test different recurrence patterns
   - Verify staff availability checking
   - Test end date and max occurrence limits
   - Verify cancellation functionality

2. **Multiple Services**
   - Test with various service combinations
   - Verify duration and price calculations
   - Test availability checking with extended durations
   - Verify booking confirmation displays

3. **Integration**
   - Test with existing single-service bookings
   - Verify points system integration
   - Test notification systems
   - Verify reporting and analytics

## Future Enhancements

Potential future improvements:

1. **Advanced Recurrence Patterns**: Custom intervals, specific days of week
2. **Bulk Operations**: Bulk modify recurring series
3. **Service Dependencies**: Define service order for multiple services
4. **Advanced Analytics**: Detailed reporting on recurring booking patterns
5. **Customer Preferences**: Save preferred service combinations

## Support

For technical support or questions about these enhancements, refer to the main project documentation or contact the development team.
