<?php
/**
 * Installation Script for Booking System Enhancements
 * Run this script to automatically set up the new booking features
 */

require_once __DIR__ . '/config/app.php';

// Only allow admin access
if (!isLoggedIn() || $_SESSION['user_role'] !== 'ADMIN') {
    die('This installation script can only be run by administrators.');
}

$step = $_GET['step'] ?? 1;
$action = $_POST['action'] ?? '';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Enhancements Installation - Flix Salonce</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); }
        .glass-effect { background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); }
    </style>
</head>
<body class="min-h-screen text-white">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="glass-effect rounded-2xl p-8 mb-8">
                <h1 class="text-3xl font-bold text-center mb-4">
                    <span class="text-yellow-400">Flix Salonce</span> Booking Enhancements
                </h1>
                <p class="text-center text-gray-300">Installation Wizard</p>
                
                <!-- Progress Bar -->
                <div class="mt-6">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-300">Installation Progress</span>
                        <span class="text-sm text-gray-300"><?= min($step, 4) ?>/4</span>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-2">
                        <div class="bg-yellow-400 h-2 rounded-full transition-all duration-300" style="width: <?= (min($step, 4) / 4) * 100 ?>%"></div>
                    </div>
                </div>
            </div>

            <?php if ($step == 1): ?>
                <!-- Step 1: Welcome & Prerequisites -->
                <div class="glass-effect rounded-2xl p-8">
                    <h2 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-info-circle text-yellow-400 mr-3"></i>
                        Welcome to Booking Enhancements Setup
                    </h2>
                    
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-xl font-semibold mb-3 text-yellow-400">What's Being Installed</h3>
                            <ul class="space-y-2 text-gray-300">
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i> Recurring Bookings Feature</li>
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i> Multiple Service Selection</li>
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i> Enhanced Customer Interface</li>
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i> Admin Management Tools</li>
                                <li class="flex items-center"><i class="fas fa-check text-green-400 mr-2"></i> API Endpoints</li>
                            </ul>
                        </div>
                        
                        <div>
                            <h3 class="text-xl font-semibold mb-3 text-yellow-400">Prerequisites Check</h3>
                            <div class="space-y-2">
                                <?php
                                $prerequisites = [
                                    'Database Connection' => $database ? true : false,
                                    'Admin Access' => ($_SESSION['user_role'] === 'ADMIN'),
                                    'Write Permissions' => is_writable(__DIR__),
                                    'Required Files' => file_exists(__DIR__ . '/database/booking_enhancements.sql')
                                ];
                                
                                $allGood = true;
                                foreach ($prerequisites as $check => $status) {
                                    $allGood = $allGood && $status;
                                    echo "<div class='flex items-center'>";
                                    echo "<i class='fas " . ($status ? 'fa-check text-green-400' : 'fa-times text-red-400') . " mr-2'></i>";
                                    echo "<span class='" . ($status ? 'text-green-400' : 'text-red-400') . "'>$check</span>";
                                    echo "</div>";
                                }
                                ?>
                            </div>
                        </div>
                        
                        <?php if ($allGood): ?>
                            <div class="bg-green-900/30 border border-green-500 rounded-lg p-4">
                                <p class="text-green-400 font-semibold">✓ All prerequisites met! Ready to proceed.</p>
                            </div>
                            
                            <div class="flex justify-end">
                                <a href="?step=2" class="bg-yellow-400 text-black px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                                    Continue to Database Setup <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="bg-red-900/30 border border-red-500 rounded-lg p-4">
                                <p class="text-red-400 font-semibold">⚠ Please resolve the issues above before continuing.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Setup -->
                <div class="glass-effect rounded-2xl p-8">
                    <h2 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-database text-yellow-400 mr-3"></i>
                        Database Setup
                    </h2>
                    
                    <?php if ($action === 'install_db'): ?>
                        <div class="mb-6">
                            <?php
                            try {
                                $errors = [];
                                $successCount = 0;

                                // Execute database changes step by step
                                $steps = [
                                    "Add recurring_series_id column" => "ALTER TABLE bookings ADD COLUMN recurring_series_id VARCHAR(36) NULL",
                                    "Add is_recurring column" => "ALTER TABLE bookings ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE",
                                    "Add has_multiple_services column" => "ALTER TABLE bookings ADD COLUMN has_multiple_services BOOLEAN DEFAULT FALSE",
                                    "Create recurring_booking_series table" => "CREATE TABLE IF NOT EXISTS recurring_booking_series (
                                        id VARCHAR(36) PRIMARY KEY,
                                        user_id VARCHAR(36) NOT NULL,
                                        service_id VARCHAR(36),
                                        package_id VARCHAR(36),
                                        staff_id VARCHAR(36),
                                        title VARCHAR(255) NOT NULL,
                                        recurrence_type ENUM('WEEKLY', 'BI_WEEKLY', 'MONTHLY') NOT NULL,
                                        start_date DATE NOT NULL,
                                        end_date DATE,
                                        max_occurrences INT,
                                        start_time TIME NOT NULL,
                                        end_time TIME NOT NULL,
                                        total_amount DECIMAL(10,2) NOT NULL,
                                        points_used INT DEFAULT 0,
                                        notes TEXT,
                                        is_active BOOLEAN DEFAULT TRUE,
                                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                                        FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE SET NULL,
                                        FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE SET NULL,
                                        FOREIGN KEY (staff_id) REFERENCES users(id) ON DELETE SET NULL
                                    )",
                                    "Create booking_services table" => "CREATE TABLE IF NOT EXISTS booking_services (
                                        id VARCHAR(36) PRIMARY KEY,
                                        booking_id VARCHAR(36) NOT NULL,
                                        service_id VARCHAR(36) NOT NULL,
                                        service_price DECIMAL(10,2) NOT NULL,
                                        service_duration INT NOT NULL,
                                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                        FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
                                        FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
                                        UNIQUE KEY unique_booking_service (booking_id, service_id)
                                    )",
                                    "Add foreign key constraint" => "ALTER TABLE bookings ADD CONSTRAINT fk_bookings_recurring_series FOREIGN KEY (recurring_series_id) REFERENCES recurring_booking_series(id) ON DELETE SET NULL",
                                    "Create index on recurring_series_id" => "CREATE INDEX idx_bookings_recurring_series ON bookings(recurring_series_id)",
                                    "Create index on is_recurring" => "CREATE INDEX idx_bookings_is_recurring ON bookings(is_recurring)",
                                    "Create index on has_multiple_services" => "CREATE INDEX idx_bookings_has_multiple_services ON bookings(has_multiple_services)",
                                    "Create index on recurring series user" => "CREATE INDEX idx_recurring_series_user ON recurring_booking_series(user_id)",
                                    "Create index on recurring series active" => "CREATE INDEX idx_recurring_series_active ON recurring_booking_series(is_active)",
                                    "Create index on booking services booking" => "CREATE INDEX idx_booking_services_booking ON booking_services(booking_id)",
                                    "Create index on booking services service" => "CREATE INDEX idx_booking_services_service ON booking_services(service_id)"
                                ];

                                foreach ($steps as $stepName => $sql) {
                                    try {
                                        $database->query($sql);
                                        $successCount++;
                                        echo "<div class='text-green-400 text-sm mb-1'>✓ $stepName</div>";
                                    } catch (Exception $e) {
                                        // Check if it's just a "column already exists" or "table already exists" error
                                        if (strpos($e->getMessage(), 'Duplicate column name') !== false ||
                                            strpos($e->getMessage(), 'already exists') !== false ||
                                            strpos($e->getMessage(), 'Duplicate key name') !== false) {
                                            echo "<div class='text-yellow-400 text-sm mb-1'>⚠ $stepName (already exists)</div>";
                                            $successCount++;
                                        } else {
                                            $errors[] = "$stepName: " . $e->getMessage();
                                            echo "<div class='text-red-400 text-sm mb-1'>✗ $stepName</div>";
                                        }
                                    }
                                }

                                if (empty($errors)) {
                                    echo "<div class='bg-green-900/30 border border-green-500 rounded-lg p-4 mb-4 mt-4'>";
                                    echo "<p class='text-green-400 font-semibold'>✓ Database setup completed successfully!</p>";
                                    echo "<p class='text-green-300'>Completed $successCount steps.</p>";
                                    echo "</div>";

                                    echo "<div class='flex justify-end'>";
                                    echo "<a href='?step=3' class='bg-yellow-400 text-black px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors'>";
                                    echo "Continue to File Verification <i class='fas fa-arrow-right ml-2'></i>";
                                    echo "</a>";
                                    echo "</div>";
                                } else {
                                    echo "<div class='bg-red-900/30 border border-red-500 rounded-lg p-4 mb-4 mt-4'>";
                                    echo "<p class='text-red-400 font-semibold'>⚠ Database setup completed with some errors:</p>";
                                    echo "<ul class='mt-2 text-red-300'>";
                                    foreach ($errors as $error) {
                                        echo "<li>• $error</li>";
                                    }
                                    echo "</ul>";
                                    echo "<p class='text-yellow-300 mt-2'>You may still be able to continue if the core tables were created.</p>";
                                    echo "</div>";

                                    echo "<div class='flex justify-end'>";
                                    echo "<a href='?step=3' class='bg-yellow-400 text-black px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors'>";
                                    echo "Continue Anyway <i class='fas fa-arrow-right ml-2'></i>";
                                    echo "</a>";
                                    echo "</div>";
                                }

                            } catch (Exception $e) {
                                echo "<div class='bg-red-900/30 border border-red-500 rounded-lg p-4'>";
                                echo "<p class='text-red-400 font-semibold'>⚠ Database setup failed: " . $e->getMessage() . "</p>";
                                echo "</div>";
                            }
                            ?>
                        </div>
                    <?php else: ?>
                        <div class="space-y-6">
                            <p class="text-gray-300">This step will create the necessary database tables and structures for the booking enhancements.</p>
                            
                            <div class="bg-blue-900/30 border border-blue-500 rounded-lg p-4">
                                <h3 class="text-blue-400 font-semibold mb-2">What will be created:</h3>
                                <ul class="text-blue-300 space-y-1">
                                    <li>• <code>recurring_booking_series</code> table</li>
                                    <li>• <code>booking_services</code> table</li>
                                    <li>• New columns in <code>bookings</code> table</li>
                                    <li>• Database indexes for performance</li>
                                    <li>• Helpful views and stored procedures</li>
                                </ul>
                            </div>
                            
                            <form method="POST" class="flex justify-between">
                                <a href="?step=1" class="bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-arrow-left mr-2"></i> Back
                                </a>
                                <button type="submit" name="action" value="install_db" class="bg-yellow-400 text-black px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                                    Install Database Changes <i class="fas fa-database ml-2"></i>
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>

            <?php elseif ($step == 3): ?>
                <!-- Step 3: File Verification -->
                <div class="glass-effect rounded-2xl p-8">
                    <h2 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-file-check text-yellow-400 mr-3"></i>
                        File Verification
                    </h2>
                    
                    <div class="space-y-6">
                        <p class="text-gray-300">Verifying that all required files are in place...</p>
                        
                        <?php
                        $requiredFiles = [
                            'includes/recurring_booking_functions.php' => 'Recurring booking functions',
                            'includes/multiple_services_functions.php' => 'Multiple services functions',
                            'api/customer/recurring-bookings.php' => 'Customer recurring bookings API',
                            'api/customer/multiple-services.php' => 'Multiple services API',
                            'api/admin/recurring-bookings.php' => 'Admin recurring bookings API',
                            'customer/recurring-bookings.php' => 'Customer recurring bookings interface',
                            'admin/recurring-bookings.php' => 'Admin recurring bookings interface'
                        ];
                        
                        $allFilesExist = true;
                        echo "<div class='space-y-2'>";
                        foreach ($requiredFiles as $file => $description) {
                            $exists = file_exists(__DIR__ . '/' . $file);
                            $allFilesExist = $allFilesExist && $exists;
                            
                            echo "<div class='flex items-center justify-between p-3 bg-gray-800/50 rounded-lg'>";
                            echo "<span class='text-gray-300'>$description</span>";
                            echo "<div class='flex items-center'>";
                            echo "<code class='text-sm text-gray-400 mr-3'>$file</code>";
                            echo "<i class='fas " . ($exists ? 'fa-check text-green-400' : 'fa-times text-red-400') . "'></i>";
                            echo "</div>";
                            echo "</div>";
                        }
                        echo "</div>";
                        
                        if ($allFilesExist) {
                            echo "<div class='bg-green-900/30 border border-green-500 rounded-lg p-4'>";
                            echo "<p class='text-green-400 font-semibold'>✓ All required files are present!</p>";
                            echo "</div>";
                        } else {
                            echo "<div class='bg-red-900/30 border border-red-500 rounded-lg p-4'>";
                            echo "<p class='text-red-400 font-semibold'>⚠ Some files are missing. Please ensure all files have been uploaded correctly.</p>";
                            echo "</div>";
                        }
                        ?>
                        
                        <div class="flex justify-between">
                            <a href="?step=2" class="bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i> Back
                            </a>
                            <a href="?step=4" class="bg-yellow-400 text-black px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                                Continue to Completion <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                </div>

            <?php elseif ($step == 4): ?>
                <!-- Step 4: Completion -->
                <div class="glass-effect rounded-2xl p-8">
                    <h2 class="text-2xl font-bold mb-6 flex items-center">
                        <i class="fas fa-check-circle text-green-400 mr-3"></i>
                        Installation Complete!
                    </h2>
                    
                    <div class="space-y-6">
                        <div class="bg-green-900/30 border border-green-500 rounded-lg p-6">
                            <h3 class="text-green-400 font-semibold text-xl mb-3">🎉 Congratulations!</h3>
                            <p class="text-green-300">The booking system enhancements have been successfully installed.</p>
                        </div>
                        
                        <div>
                            <h3 class="text-xl font-semibold mb-3 text-yellow-400">What's New</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="bg-gray-800/50 rounded-lg p-4">
                                    <h4 class="font-semibold text-white mb-2">For Customers</h4>
                                    <ul class="text-gray-300 space-y-1 text-sm">
                                        <li>• Create recurring appointments</li>
                                        <li>• Book multiple services at once</li>
                                        <li>• Rebook previous appointments</li>
                                        <li>• Manage recurring series</li>
                                    </ul>
                                </div>
                                <div class="bg-gray-800/50 rounded-lg p-4">
                                    <h4 class="font-semibold text-white mb-2">For Administrators</h4>
                                    <ul class="text-gray-300 space-y-1 text-sm">
                                        <li>• View all recurring series</li>
                                        <li>• Generate recurring bookings</li>
                                        <li>• Manage multiple service bookings</li>
                                        <li>• Advanced booking analytics</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-xl font-semibold mb-3 text-yellow-400">Next Steps</h3>
                            <div class="space-y-3">
                                <a href="<?= getBasePath() ?>/test_booking_enhancements.php" 
                                   class="block bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 transition-colors">
                                    <i class="fas fa-vial mr-2"></i>
                                    <strong>Run System Tests</strong>
                                    <span class="block text-sm text-blue-200 mt-1">Verify everything is working correctly</span>
                                </a>
                                
                                <a href="<?= getBasePath() ?>/admin/recurring-bookings" 
                                   class="block bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-cog mr-2"></i>
                                    <strong>Admin Recurring Bookings</strong>
                                    <span class="block text-sm text-purple-200 mt-1">Manage recurring appointment series</span>
                                </a>
                                
                                <a href="<?= getBasePath() ?>/customer/book" 
                                   class="block bg-green-600 text-white p-4 rounded-lg hover:bg-green-700 transition-colors">
                                    <i class="fas fa-calendar-plus mr-2"></i>
                                    <strong>Try New Booking Features</strong>
                                    <span class="block text-sm text-green-200 mt-1">Test the enhanced booking interface</span>
                                </a>
                                
                                <a href="<?= getBasePath() ?>/admin" 
                                   class="block bg-gray-600 text-white p-4 rounded-lg hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-home mr-2"></i>
                                    <strong>Return to Admin Dashboard</strong>
                                    <span class="block text-sm text-gray-200 mt-1">Go back to the main admin area</span>
                                </a>
                            </div>
                        </div>
                        
                        <div class="bg-blue-900/30 border border-blue-500 rounded-lg p-4">
                            <h4 class="text-blue-400 font-semibold mb-2">📚 Documentation</h4>
                            <p class="text-blue-300 text-sm">
                                For detailed information about the new features, please refer to 
                                <code>BOOKING_ENHANCEMENTS_README.md</code> in your project root directory.
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
