<?php
require_once __DIR__ . '/../../config/app.php';
require_once __DIR__ . '/../../includes/customer_panel_functions.php';

// Check if user is customer
if (!isLoggedIn() || $_SESSION['user_role'] !== 'CUSTOMER') {
    redirect('/auth/login.php');
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action']) && $_POST['action'] === 'create_booking') {
            require_once __DIR__ . '/../includes/multiple_services_functions.php';
            require_once __DIR__ . '/../includes/recurring_booking_functions.php';

            $bookingType = $_POST['booking_type'] ?? 'single_service';
            $isRecurring = isset($_POST['make_recurring']) && $_POST['make_recurring'] === 'on';

            // Prepare base booking data
            $bookingData = [
                'staff_id' => $_POST['staff_id'],
                'date' => $_POST['date'],
                'start_time' => $_POST['start_time'],
                'end_time' => $_POST['end_time'],
                'points_used' => intval($_POST['points_used'] ?? 0),
                'notes' => $_POST['notes'] ?? null
            ];

            // Handle different booking types
            if ($bookingType === 'multiple_services' && !empty($_POST['selected_services'])) {
                // Multiple services booking
                $selectedServices = json_decode($_POST['selected_services'], true);
                $bookingData['services'] = $selectedServices;

                if ($isRecurring) {
                    // Recurring booking with multiple services
                    $bookingData['recurrence_type'] = $_POST['recurrence_type'];
                    $bookingData['end_date'] = $_POST['end_option'] === 'date' ? $_POST['end_date'] : null;
                    $bookingData['max_occurrences'] = $_POST['end_option'] === 'occurrences' ? intval($_POST['max_occurrences']) : null;
                    $bookingData['title'] = $_POST['series_title'] ?: 'Recurring Multiple Services';

                    $result = createRecurringBookingWithMultipleServices($_SESSION['user_id'], $bookingData);
                    $bookingId = $result['success'] ? $result['series_id'] : null;
                    $message = $result['success'] ? $result['message'] : $result['error'];
                } else {
                    // Single booking with multiple services
                    $result = createBookingWithMultipleServices($_SESSION['user_id'], $bookingData);
                    $bookingId = $result['success'] ? $result['id'] : null;
                    $message = $result['success'] ? 'Multiple services booking created successfully!' : $result['error'];
                }
            } else {
                // Single service or package booking
                $bookingData['service_id'] = !empty($_POST['service_id']) ? $_POST['service_id'] : null;
                $bookingData['package_id'] = !empty($_POST['package_id']) ? $_POST['package_id'] : null;

                if ($isRecurring) {
                    // Recurring single service/package booking
                    $bookingData['recurrence_type'] = $_POST['recurrence_type'];
                    $bookingData['end_date'] = $_POST['end_option'] === 'date' ? $_POST['end_date'] : null;
                    $bookingData['max_occurrences'] = $_POST['end_option'] === 'occurrences' ? intval($_POST['max_occurrences']) : null;
                    $bookingData['title'] = $_POST['series_title'] ?: 'Recurring Appointment';

                    // Calculate total amount for recurring booking
                    if ($bookingData['service_id']) {
                        $service = $database->fetch("SELECT price FROM services WHERE id = ?", [$bookingData['service_id']]);
                        $bookingData['total_amount'] = $service['price'];
                    } elseif ($bookingData['package_id']) {
                        $package = $database->fetch("SELECT price FROM packages WHERE id = ?", [$bookingData['package_id']]);
                        $bookingData['total_amount'] = $package['price'];
                    }

                    $result = createRecurringBookingSeries($_SESSION['user_id'], $bookingData);
                    $bookingId = $result['success'] ? $result['series_id'] : null;
                    $message = $result['success'] ? $result['message'] : $result['error'];
                } else {
                    // Regular single booking
                    $bookingId = createCustomerBooking($_SESSION['user_id'], $bookingData);
                    $message = 'Appointment booked successfully! Booking ID: ' . $bookingId;
                }
            }

            if (!$bookingId) {
                throw new Exception($message ?? 'Failed to create booking');
            }

            // Send booking acknowledgment email
            $userId = $_SESSION['user_id'];
            $user = $database->fetch("SELECT * FROM users WHERE id = ?", [$userId]);
            
            if ($user) {
                // Get service/package details
                $serviceName = '';
                $duration = '';
                $price = '';
                
                if ($bookingData['service_id']) {
                    $service = $database->fetch("SELECT * FROM services WHERE id = ?", [$bookingData['service_id']]);
                    $serviceName = $service['name'];
                    $duration = $service['duration'];
                    $price = formatCurrency($service['price']);
                } else if ($bookingData['package_id']) {
                    $package = $database->fetch("SELECT * FROM packages WHERE id = ?", [$bookingData['package_id']]);
                    $serviceName = $package['name'];
                    $duration = $package['duration'];
                    $price = formatCurrency($package['price']);
                }

                // Get staff details
                $staff = $database->fetch("SELECT * FROM users WHERE id = ?", [$bookingData['staff_id']]);
                $staffName = $staff ? $staff['name'] : 'Selected Staff';

                $subject = "Booking Request Received - " . APP_NAME;
                
                $body = '
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                            <h1 style="margin: 0; font-size: 28px;">' . APP_NAME . '</h1>
                            <p style="margin: 10px 0 0 0; opacity: 0.9;">Booking Request Received</p>
                        </div>
                        
                        <div style="padding: 30px; background: #f8f9fa;">
                            <h2 style="color: #333; margin-top: 0;">Dear ' . htmlspecialchars($user['name']) . ',</h2>
                            <p style="color: #666; line-height: 1.6;">Thank you for choosing ' . APP_NAME . '. We have received your booking request and our team will review it shortly.</p>
                            
                            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                                <h3 style="margin-top: 0; color: #333;">Booking Details</h3>
                                <p><strong>Service:</strong> ' . htmlspecialchars($serviceName) . '</p>
                                <p><strong>Date:</strong> ' . formatDate($bookingData['date']) . '</p>
                                <p><strong>Time:</strong> ' . formatTime($bookingData['start_time']) . ' - ' . formatTime($bookingData['end_time']) . '</p>
                                <p><strong>Duration:</strong> ' . $duration . ' minutes</p>
                                <p><strong>Staff:</strong> ' . htmlspecialchars($staffName) . '</p>
                                <p><strong>Price:</strong> ' . $price . '</p>
                            </div>
                            
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                                <p style="margin: 0; color: #1565c0;">
                                    <strong>What\'s Next?</strong><br>
                                    1. Our team will review your booking request<br>
                                    2. You will receive a confirmation email once approved<br>
                                    3. You can view your booking status in your dashboard
                                </p>
                            </div>
                            
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="' . getBaseUrl() . '/customer/bookings" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">View My Bookings</a>
                            </div>
                            
                            <p style="color: #666; line-height: 1.6;">If you have any questions or need to make changes to your booking, please don\'t hesitate to contact us.</p>
                        </div>
                        
                        <div style="background: #333; color: white; padding: 20px; text-align: center;">
                            <p style="margin: 0;">' . APP_NAME . ' | +255 123 456 789 | ' . SMTP_FROM_EMAIL . '</p>
                        </div>
                    </div>
                ';

                // Send the email
                sendSMTPEmail($user['email'], $subject, $body);
            }

            $message = 'Appointment booked successfully! Booking ID: ' . $bookingId;
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get customer data
$customerId = $_SESSION['user_id'];
$profile = getCustomerProfile($customerId);

// Get services, packages, and staff
global $database;

// Pagination settings for services
$servicesPerPage = 10;
$currentPage = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($currentPage - 1) * $servicesPerPage;

// Get total number of services
$totalServices = $database->fetchAll("SELECT COUNT(*) as count FROM services WHERE is_active = 1")[0]['count'];
$totalPages = ceil($totalServices / $servicesPerPage);

// Get paginated services
$services = $database->fetchAll(
    "SELECT * FROM services WHERE is_active = 1 ORDER BY name ASC LIMIT ? OFFSET ?", 
    [$servicesPerPage, $offset]
);

$packages = $database->fetchAll("SELECT * FROM packages WHERE is_active = 1 ORDER BY name ASC");
$staff = $database->fetchAll("
    SELECT DISTINCT u.*, GROUP_CONCAT(
        CONCAT(s.name, ' (', ss.proficiency_level, ')')
        SEPARATOR '|'
    ) as specialties 
    FROM users u
    LEFT JOIN staff_specialties ss ON u.id = ss.user_id
    LEFT JOIN services s ON ss.service_id = s.id
    WHERE u.role = 'STAFF' 
    GROUP BY u.id
    ORDER BY u.name ASC
");

// Get package services for each package
foreach ($packages as $index => $package) {
    $packages[$index]['services'] = $database->fetchAll("
        SELECT s.* FROM services s
        INNER JOIN package_services ps ON s.id = ps.service_id
        WHERE ps.package_id = ?
        ORDER BY s.name
    ", [$package['id']]);

    // Calculate total duration for package
    $packages[$index]['total_duration'] = array_sum(array_column($packages[$index]['services'], 'duration'));

    // Calculate savings
    $originalPrice = array_sum(array_column($packages[$index]['services'], 'price'));
    $packages[$index]['savings'] = max(0, $originalPrice - $package['price']);
}

$pageTitle = "Book Appointment";

// Include customer header
include __DIR__ . '/../../includes/customer_header.php';
?>

<!-- Add this right after the opening body tag -->
<div id="notification" class="fixed top-4 right-4 z-50 transform transition-all duration-300 translate-x-full opacity-0 flex items-center p-3 sm:p-4 rounded-xl border backdrop-blur-sm bg-secondary-900/90 border-salon-gold/30 shadow-lg mx-4 sm:mx-0" style="min-width: auto; max-width: calc(100vw - 2rem); width: fit-content; font-size: 0.875rem;">
    <div class="flex-shrink-0 text-salon-gold mr-2 sm:mr-3">
        <svg class="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
        </svg>
    </div>
    <div class="flex-1 mr-2">
        <div id="notificationMessage" class="text-white text-sm sm:text-base"></div>
    </div>
    <button onclick="hideNotification()" class="flex-shrink-0 text-gray-400 hover:text-white transition-colors p-1">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
    </button>
</div>

<!-- Page Header -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-2xl shadow-xl mb-8 hover-lift">
    <div class="px-6 sm:px-8 lg:px-10">
        <div class="py-8 md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    <a href="<?= getBasePath() ?>/customer" class="text-gray-400 hover:text-salon-gold transition-all duration-300 mr-6 p-2 rounded-lg hover:bg-salon-gold/10">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </a>
                    <div>
                        <div class="flex items-center">
                            <h1 class="text-3xl font-bold leading-7 text-white sm:leading-9 sm:truncate font-serif">
                                Book New <span class="text-salon-gold">Appointment</span>
                            </h1>
                        </div>
                        <dl class="mt-3 flex flex-col sm:mt-2 sm:flex-row sm:flex-wrap">
                            <dt class="sr-only">Page description</dt>
                            <dd class="flex items-center text-lg text-gray-300 font-medium sm:mr-6">
                                Choose your service, preferred staff, and schedule your visit
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Message Display -->
<?php if ($message): ?>
    <div class="mb-8 p-6 rounded-xl <?= $messageType === 'success' ? 'bg-salon-gold/10 border border-salon-gold text-salon-gold' : 'bg-red-500/10 border border-red-500 text-red-400' ?> backdrop-blur-sm">
        <div class="flex items-center">
            <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?> mr-3 text-xl"></i>
            <div>
                <?= htmlspecialchars($message) ?>
                <?php if ($messageType === 'success'): ?>
                    <div class="mt-2">
                        <a href="<?= getBasePath() ?>/customer/bookings" class="text-salon-gold hover:text-gold-light underline font-medium transition-colors duration-200">View your bookings</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Booking Form -->
<div class="bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 overflow-hidden shadow-xl rounded-2xl hover-lift">
    <div class="p-8">
        <form id="bookingForm" method="POST" class="space-y-8">
            <input type="hidden" name="action" value="create_booking">

                    <!-- Step 1: Service or Package Selection -->
                    <div class="booking-step" id="step1">
                        <h3 class="text-2xl font-bold text-white mb-6 font-serif">
                            <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-salon-gold text-black text-lg font-bold mr-4">1</span>
                            Choose Service(s) or Package
                        </h3>

                        <!-- Booking Type Selection -->
                        <div class="mb-6">
                            <div class="flex flex-wrap gap-4">
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="booking_type" value="single_service" checked
                                           class="text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                    <span class="ml-2 text-white">Single Service</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="booking_type" value="multiple_services"
                                           class="text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                    <span class="ml-2 text-white">Multiple Services</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="booking_type" value="package"
                                           class="text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                    <span class="ml-2 text-white">Package</span>
                                </label>
                                <label class="flex items-center cursor-pointer">
                                    <input type="radio" name="booking_type" value="rebook_previous"
                                           class="text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                    <span class="ml-2 text-white">Rebook Previous</span>
                                </label>
                            </div>
                        </div>

                        <!-- Search and Filter Section -->
                        <div class="mb-6 bg-secondary-900/80 backdrop-blur-sm border border-secondary-700 rounded-xl p-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <!-- Search Bar -->
                                <div class="md:col-span-2">
                                    <div class="relative">
                                        <input type="text" 
                                               id="searchInput" 
                                               placeholder="Search services or packages..." 
                                               class="w-full px-4 py-3 bg-secondary-800 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-transparent">
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <i class="fas fa-search text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>
                                <!-- Category Filter -->
                                <div>
                                    <select id="categoryFilter" class="w-full px-4 py-3 bg-secondary-800 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                        <option value="">All Categories</option>
                                        <?php
                                        $categories = array_unique(array_column($services, 'category'));
                                        foreach ($categories as $category):
                                        ?>
                                            <option value="<?= htmlspecialchars($category) ?>"><?= htmlspecialchars($category) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Active Filters -->
                            <div id="activeFilters" class="mt-4 flex flex-wrap gap-2"></div>
                        </div>

                        <!-- Selection Type Tabs -->
                        <div class="flex mb-8 bg-secondary-800/50 rounded-xl p-1 border border-secondary-700" id="selectionTabs">
                            <button type="button" id="servicesTab" class="flex-1 py-3 px-6 rounded-lg text-sm font-semibold transition-all duration-300 bg-salon-gold text-black shadow-lg">
                                Single Service
                            </button>
                            <button type="button" id="multipleServicesTab" class="flex-1 py-3 px-6 rounded-lg text-sm font-semibold transition-all duration-300 text-gray-300 hover:text-salon-gold hover:bg-salon-gold/10">
                                Multiple Services
                            </button>
                            <button type="button" id="packagesTab" class="flex-1 py-3 px-6 rounded-lg text-sm font-semibold transition-all duration-300 text-gray-300 hover:text-salon-gold hover:bg-salon-gold/10">
                                Service Packages
                            </button>
                            <button type="button" id="previousBookingsTab" class="flex-1 py-3 px-6 rounded-lg text-sm font-semibold transition-all duration-300 text-gray-300 hover:text-salon-gold hover:bg-salon-gold/10">
                                Rebook Previous
                            </button>
                        </div>

                        <!-- Individual Services -->
                        <div id="servicesSection" class="selection-section">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <?php foreach ($services as $service): ?>
                                    <div class="service-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 hover-lift"
                                         data-service-id="<?= $service['id'] ?>"
                                         data-service-price="<?= $service['price'] ?>"
                                         data-service-duration="<?= $service['duration'] ?>"
                                         data-type="service">
                                        <div class="flex items-center justify-between mb-3">
                                            <h4 class="font-semibold text-white text-lg"><?= htmlspecialchars($service['name']) ?></h4>
                                            <span class="text-salon-gold font-bold text-lg"><?= CURRENCY_SYMBOL ?> <?= number_format($service['price'], 2) ?></span>
                                        </div>
                                        <p class="text-sm text-gray-400 mb-4 leading-relaxed"><?= htmlspecialchars($service['description']) ?></p>
                                        <div class="flex items-center justify-between text-sm text-gray-500">
                                            <span class="flex items-center"><i class="fas fa-clock mr-2 text-salon-gold"></i><?= $service['duration'] ?> minutes</span>
                                            <span class="flex items-center"><i class="fas fa-tag mr-2 text-salon-gold"></i><?= htmlspecialchars($service['category']) ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Pagination Controls -->
                            <?php if ($totalPages > 1): ?>
                                <div class="mt-8 flex justify-center items-center space-x-2">
                                    <?php if ($currentPage > 1): ?>
                                        <a href="?page=<?= $currentPage - 1 ?>" class="pagination-btn" data-page="<?= $currentPage - 1 ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php
                                    // Show page numbers with ellipsis
                                    $startPage = max(1, $currentPage - 2);
                                    $endPage = min($totalPages, $currentPage + 2);

                                    if ($startPage > 1) {
                                        echo '<a href="?page=1" class="pagination-btn" data-page="1">1</a>';
                                        if ($startPage > 2) {
                                            echo '<span class="px-2 text-gray-500">...</span>';
                                        }
                                    }

                                    for ($i = $startPage; $i <= $endPage; $i++) {
                                        $activeClass = $i === $currentPage ? 'bg-salon-gold text-black' : 'text-gray-300 hover:text-salon-gold hover:border-salon-gold';
                                        echo "<a href='?page=$i' class='pagination-btn $activeClass' data-page='$i'>$i</a>";
                                    }

                                    if ($endPage < $totalPages) {
                                        if ($endPage < $totalPages - 1) {
                                            echo '<span class="px-2 text-gray-500">...</span>';
                                        }
                                        echo "<a href='?page=$totalPages' class='pagination-btn' data-page='$totalPages'>$totalPages</a>";
                                    }
                                    ?>

                                    <?php if ($currentPage < $totalPages): ?>
                                        <a href="?page=<?= $currentPage + 1 ?>" class="pagination-btn" data-page="<?= $currentPage + 1 ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Service Packages -->
                        <div id="packagesSection" class="selection-section hidden">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php foreach ($packages as $package): ?>
                                    <div class="package-option border border-secondary-600 rounded-lg p-4 cursor-pointer hover:border-salon-gold transition-colors"
                                         data-package-id="<?= $package['id'] ?>"
                                         data-package-price="<?= $package['price'] ?>"
                                         data-package-duration="<?= $package['total_duration'] ?>"
                                         data-type="package">
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="font-medium text-white"><?= htmlspecialchars($package['name']) ?></h4>
                                            <div class="text-right">
                                                <span class="text-salon-gold font-semibold"><?= CURRENCY_SYMBOL ?> <?= number_format($package['price'], 2) ?></span>
                                                <?php if ($package['savings'] > 0): ?>
                                                    <div class="text-xs text-green-400">Save <?= CURRENCY_SYMBOL ?> <?= number_format($package['savings'], 2) ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <?php if (!empty($package['description'])): ?>
                                            <p class="text-sm text-gray-400 mb-2"><?= htmlspecialchars($package['description']) ?></p>
                                        <?php endif; ?>

                                        <div class="mb-2">
                                            <p class="text-xs text-gray-500 mb-1">Includes:</p>
                                            <div class="flex flex-wrap gap-1">
                                                <?php foreach ($package['services'] as $service): ?>
                                                    <span class="inline-block px-2 py-1 bg-secondary-700 text-xs rounded text-gray-300">
                                                        <?= htmlspecialchars($service['name']) ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>

                                        <div class="flex items-center justify-between text-xs text-gray-500">
                                            <span><i class="fas fa-clock mr-1"></i><?= $package['total_duration'] ?> minutes total</span>
                                            <span><i class="fas fa-box mr-1"></i><?= count($package['services']) ?> services</span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Multiple Services Section -->
                        <div id="multipleServicesSection" class="selection-section hidden">
                            <div class="mb-6">
                                <h4 class="text-lg font-semibold text-white mb-4">Select Multiple Services</h4>
                                <p class="text-gray-300 mb-4">Choose multiple services to combine in one appointment. Total duration and pricing will be calculated automatically.</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="multipleServicesGrid">
                                <?php foreach ($services as $service): ?>
                                    <div class="multiple-service-option border border-secondary-600 rounded-lg p-4 cursor-pointer hover:border-salon-gold transition-colors"
                                         data-service-id="<?= $service['id'] ?>"
                                         data-service-price="<?= $service['price'] ?>"
                                         data-service-duration="<?= $service['duration'] ?>">

                                        <div class="flex justify-between items-start mb-3">
                                            <div class="flex items-center">
                                                <input type="checkbox" class="service-checkbox mr-3 text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800"
                                                       value="<?= $service['id'] ?>">
                                                <h4 class="text-lg font-semibold text-white"><?= htmlspecialchars($service['name']) ?></h4>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-xl font-bold text-salon-gold"><?= formatCurrency($service['price']) ?></div>
                                                <div class="text-sm text-gray-400"><?= $service['duration'] ?> min</div>
                                            </div>
                                        </div>

                                        <p class="text-gray-300 text-sm mb-3"><?= htmlspecialchars($service['description']) ?></p>

                                        <?php if ($service['category']): ?>
                                            <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-salon-gold/20 text-salon-gold">
                                                <?= htmlspecialchars($service['category']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Selected Services Summary -->
                            <div id="selectedServicesSummary" class="mt-6 bg-secondary-800/50 border border-secondary-600 rounded-lg p-4 hidden">
                                <h5 class="text-lg font-semibold text-white mb-4">Selected Services Summary</h5>
                                <div id="selectedServicesList" class="space-y-2 mb-4"></div>
                                <div class="border-t border-secondary-600 pt-4">
                                    <div class="flex justify-between items-center text-lg font-semibold">
                                        <span class="text-white">Total Duration:</span>
                                        <span class="text-salon-gold" id="totalDuration">0 minutes</span>
                                    </div>
                                    <div class="flex justify-between items-center text-lg font-semibold">
                                        <span class="text-white">Total Price:</span>
                                        <span class="text-salon-gold" id="totalPrice"><?= formatCurrency(0) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Previous Bookings Section -->
                        <div id="previousBookingsSection" class="selection-section hidden">
                            <div class="mb-6">
                                <h4 class="text-lg font-semibold text-white mb-4">Rebook Previous Service</h4>
                                <p class="text-gray-300 mb-4">Select from your previous bookings to quickly recreate the same appointment.</p>
                            </div>

                            <div id="previousBookingsGrid" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Previous bookings will be loaded here via JavaScript -->
                            </div>

                            <div id="previousBookingsLoading" class="text-center py-12">
                                <div class="text-gray-400">
                                    <i class="fas fa-spinner fa-spin text-2xl mb-4"></i>
                                    <p>Loading your previous bookings...</p>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" id="service_id" name="service_id">
                        <input type="hidden" id="package_id" name="package_id">
                        <input type="hidden" id="selected_services" name="selected_services">
                        <input type="hidden" id="booking_type" name="booking_type" value="single_service">
                    </div>

                    <!-- Step 2: Staff Selection -->
                    <div class="booking-step hidden" id="step2">
                        <h3 class="text-2xl font-bold text-white mb-6 font-serif">
                            <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-salon-gold text-black text-lg font-bold mr-4">2</span>
                            Choose Staff Member
                        </h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <?php foreach ($staff as $member): ?>
                                <div class="staff-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 hover-lift relative"
                                     data-staff-id="<?= $member['id'] ?>">
                                    <?php if (isset($member['is_best_fit']) && $member['is_best_fit']): ?>
                                        <div class="absolute -top-3 -right-3 w-12 h-12 flex items-center justify-center">
                                            <div class="absolute inset-0 bg-salon-gold rounded-full animate-ping opacity-20"></div>
                                            <div class="absolute inset-0 bg-salon-gold rounded-full"></div>
                                            <i class="fas fa-crown text-black text-lg relative z-10"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="flex items-center gap-4 mb-4">
                                        <div class="w-16 h-16 rounded-full <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'bg-salon-gold' : 'bg-secondary-800' ?> flex items-center justify-center border-2 <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'border-salon-gold shadow-lg shadow-salon-gold/20' : 'border-secondary-700' ?>">
                                            <i class="fas fa-user <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'text-black' : 'text-gray-400' ?> text-xl"></i>
                                        </div>
                                        <div>
                                            <div class="flex items-center gap-2">
                                                <h4 class="font-semibold text-white text-lg"><?= htmlspecialchars($member['name']) ?></h4>
                                                <?php if (isset($member['is_best_fit']) && $member['is_best_fit']): ?>
                                                    <span class="px-2 py-1 bg-salon-gold/20 text-salon-gold text-xs rounded-full">
                                                        Best Match
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <p class="text-sm text-salon-gold font-medium"><?= htmlspecialchars($member['role']) ?></p>
                                        </div>
                                    </div>

                                    <?php 
                                    $specialtiesList = !empty($member['specialties']) ? explode('|', $member['specialties']) : [];
                                    if (!empty($specialtiesList)): 
                                    ?>
                                        <div class="flex flex-wrap gap-2 mb-3">
                                            <?php foreach ($specialtiesList as $specialty): ?>
                                                <span class="inline-block px-3 py-1 bg-secondary-800/50 border <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'border-salon-gold/30' : 'border-secondary-700' ?> text-xs rounded-full <?= (isset($member['is_best_fit']) && $member['is_best_fit']) ? 'text-salon-gold' : 'text-gray-300' ?>">
                                                    <?= htmlspecialchars($specialty) ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <input type="hidden" id="staff_id" name="staff_id" required>
                    </div>

                    <!-- Step 3: Date & Time Selection -->
                    <div class="booking-step hidden" id="step3">
                        <h3 class="text-2xl font-bold text-white mb-6 font-serif">
                            <span class="inline-flex items-center justify-center w-10 h-10 rounded-full bg-salon-gold text-black text-lg font-bold mr-4">3</span>
                            Choose Date & Time
                        </h3>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div>
                                <label for="date" class="block text-sm font-semibold text-salon-gold mb-3">Select Date</label>
                                <input type="date" id="date" name="date" required min="<?= date('Y-m-d') ?>"
                                       class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                            </div>

                            <div>
                                <label for="start_time" class="block text-sm font-semibold text-salon-gold mb-3">Start Time</label>
                                <input type="time" id="start_time" name="start_time" required
                                       class="w-full px-4 py-3 bg-secondary-800/50 border border-secondary-700 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-salon-gold focus:border-salon-gold transition-all duration-300">
                                <input type="hidden" id="end_time" name="end_time" required>

                                <!-- Availability Status -->
                                <div id="availabilityStatus" class="mt-4 hidden">
                                    <div id="availabilityMessage" class="text-sm p-4 rounded-xl flex items-center"></div>
                                </div>

                                <!-- Time Input Help -->
                                <div class="mt-3">
                                    <p class="text-sm text-gray-400 flex items-center">
                                        <i class="fas fa-info-circle mr-2 text-salon-gold"></i>
                                        End time will be calculated automatically based on service duration
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Additional Options -->
                    <div class="booking-step hidden" id="step4">
                        <h3 class="text-lg font-semibold text-white mb-4">
                            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-salon-gold text-black text-sm font-bold mr-3">4</span>
                            Additional Options
                        </h3>
                        
                        <div class="space-y-4">
                            <!-- Points Usage -->
                            <div class="border border-secondary-600 rounded-lg p-4">
                                <h4 class="font-medium text-white mb-2">Use Points for Discount</h4>
                                <p class="text-sm text-gray-400 mb-3">You have <?= number_format($profile['points']) ?> points available (1 point = <?= CURRENCY_SYMBOL ?> 10)</p>

                                <div class="flex items-center gap-4">
                                    <div class="flex-1">
                                        <input type="number" id="points_used" name="points_used" min="0" max="<?= $profile['points'] ?>" value="0"
                                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                    </div>
                                    <div class="text-sm text-gray-400">
                                        Discount: <?= CURRENCY_SYMBOL ?> <span id="pointsDiscount">0.00</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recurring Booking Options -->
                            <div class="border border-secondary-600 rounded-lg p-4 mb-4">
                                <h4 class="font-medium text-white mb-3">Recurring Appointment Options</h4>

                                <div class="mb-4">
                                    <label class="flex items-center cursor-pointer">
                                        <input type="checkbox" id="makeRecurring" name="make_recurring"
                                               class="text-salon-gold focus:ring-salon-gold focus:ring-offset-secondary-800">
                                        <span class="ml-2 text-white">Make this a recurring appointment</span>
                                    </label>
                                </div>

                                <div id="recurringOptions" class="hidden space-y-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Repeat Every</label>
                                            <select id="recurrenceType" name="recurrence_type"
                                                    class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                                <option value="WEEKLY">Weekly</option>
                                                <option value="BI_WEEKLY">Every 2 Weeks</option>
                                                <option value="MONTHLY">Monthly</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-300 mb-2">End Option</label>
                                            <select id="endOption" name="end_option"
                                                    class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                                <option value="date">End by date</option>
                                                <option value="occurrences">Number of appointments</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div id="endDateOption">
                                            <label class="block text-sm font-medium text-gray-300 mb-2">End Date</label>
                                            <input type="date" id="endDate" name="end_date"
                                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                        </div>

                                        <div id="maxOccurrencesOption" class="hidden">
                                            <label class="block text-sm font-medium text-gray-300 mb-2">Number of Appointments</label>
                                            <input type="number" id="maxOccurrences" name="max_occurrences" min="2" max="52" value="4"
                                                   class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                        </div>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-300 mb-2">Series Title (Optional)</label>
                                        <input type="text" id="seriesTitle" name="series_title" placeholder="e.g., Monthly Hair Maintenance"
                                               class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold">
                                    </div>
                                </div>
                            </div>

                            <!-- Special Notes -->
                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-300 mb-2">Special Requests (Optional)</label>
                                <textarea id="notes" name="notes" rows="3"
                                          class="w-full px-3 py-2 bg-secondary-700 border border-secondary-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-salon-gold"
                                          placeholder="Any special requests or notes for your appointment..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Summary -->
                    <div class="booking-step hidden" id="summary">
                        <h3 class="text-lg font-semibold text-white mb-4">
                            <span class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-salon-gold text-black text-sm font-bold mr-3">5</span>
                            Booking Summary
                        </h3>
                        
                        <div class="bg-secondary-700 rounded-lg p-4">
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Service:</span>
                                    <span class="text-white" id="summaryService">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Staff:</span>
                                    <span class="text-white" id="summaryStaff">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Date & Time:</span>
                                    <span class="text-white" id="summaryDateTime">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Duration:</span>
                                    <span class="text-white" id="summaryDuration">-</span>
                                </div>
                                <hr class="border-secondary-600">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Service Price:</span>
                                    <span class="text-white" id="summaryPrice"><?= CURRENCY_SYMBOL ?> 0.00</span>
                                </div>
                                <div class="flex justify-between" id="pointsDiscountRow" style="display: none;">
                                    <span class="text-gray-400">Points Discount:</span>
                                    <span class="text-green-400" id="summaryPointsDiscount">-<?= CURRENCY_SYMBOL ?> 0.00</span>
                                </div>
                                <div class="flex justify-between font-semibold text-lg">
                                    <span class="text-white">Total:</span>
                                    <span class="text-salon-gold" id="summaryTotal"><?= CURRENCY_SYMBOL ?> 0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex items-center justify-between pt-8 border-t border-secondary-700">
                        <button type="button" id="prevBtn" onclick="changeStep(-1)"
                                class="px-8 py-3 bg-secondary-800/50 hover:bg-secondary-700 text-white rounded-xl font-semibold transition-all duration-300 hover-lift border border-secondary-700" style="display: none;">
                            <i class="fas fa-arrow-left mr-2"></i>Previous
                        </button>

                        <div class="flex-1"></div>

                        <button type="button" id="nextBtn" onclick="changeStep(1)"
                                class="px-8 py-3 bg-salon-gold hover:bg-gold-light text-black rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg">
                            Next <i class="fas fa-arrow-right ml-2"></i>
                        </button>

                        <button type="submit" id="submitBtn"
                                class="px-8 py-3 bg-salon-gold hover:bg-gold-light text-black rounded-xl font-semibold transition-all duration-300 hover:scale-105 shadow-lg" style="display: none;">
                            <i class="fas fa-check mr-2"></i>Book Appointment
                        </button>
                    </div>
        </form>
    </div>
</div>

    <script>
        let currentStep = 1;
        let selectedService = null;
        let selectedPackage = null;
        let selectedServices = [];
        let selectedStaff = null;
        let selectionType = 'service'; // 'service', 'multiple_services', 'package', or 'previous_booking'
        let previousBookings = [];

        // Tab switching
        document.getElementById('servicesTab').addEventListener('click', function() {
            switchTab('services');
        });

        document.getElementById('multipleServicesTab').addEventListener('click', function() {
            switchTab('multiple_services');
        });

        document.getElementById('packagesTab').addEventListener('click', function() {
            switchTab('packages');
        });

        document.getElementById('previousBookingsTab').addEventListener('click', function() {
            switchTab('previous_bookings');
        });

        function switchTab(type) {
            // Update tab appearance
            const servicesTab = document.getElementById('servicesTab');
            const multipleServicesTab = document.getElementById('multipleServicesTab');
            const packagesTab = document.getElementById('packagesTab');
            const previousBookingsTab = document.getElementById('previousBookingsTab');

            const servicesSection = document.getElementById('servicesSection');
            const multipleServicesSection = document.getElementById('multipleServicesSection');
            const packagesSection = document.getElementById('packagesSection');
            const previousBookingsSection = document.getElementById('previousBookingsSection');

            // Reset all tabs
            [servicesTab, multipleServicesTab, packagesTab, previousBookingsTab].forEach(tab => {
                tab.classList.remove('bg-salon-gold', 'text-black');
                tab.classList.add('text-gray-300');
            });

            // Hide all sections
            [servicesSection, multipleServicesSection, packagesSection, previousBookingsSection].forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected tab and section
            switch(type) {
                case 'services':
                    servicesTab.classList.add('bg-salon-gold', 'text-black');
                    servicesTab.classList.remove('text-gray-300');
                    servicesSection.classList.remove('hidden');
                    selectionType = 'service';
                    document.getElementById('booking_type').value = 'single_service';
                    break;

                case 'multiple_services':
                    multipleServicesTab.classList.add('bg-salon-gold', 'text-black');
                    multipleServicesTab.classList.remove('text-gray-300');
                    multipleServicesSection.classList.remove('hidden');
                    selectionType = 'multiple_services';
                    document.getElementById('booking_type').value = 'multiple_services';
                    break;

                case 'packages':
                    packagesTab.classList.add('bg-salon-gold', 'text-black');
                    packagesTab.classList.remove('text-gray-300');
                    packagesSection.classList.remove('hidden');
                    selectionType = 'package';
                    document.getElementById('booking_type').value = 'package';
                    break;

                case 'previous_bookings':
                    previousBookingsTab.classList.add('bg-salon-gold', 'text-black');
                    previousBookingsTab.classList.remove('text-gray-300');
                    previousBookingsSection.classList.remove('hidden');
                    selectionType = 'previous_booking';
                    document.getElementById('booking_type').value = 'rebook_previous';
                    loadPreviousBookings();
                    break;
            }

            // Clear previous selections
            clearSelections();
        }

        function clearSelections() {
            selectedService = null;
            selectedPackage = null;
            selectedServices = [];
            document.getElementById('service_id').value = '';
            document.getElementById('package_id').value = '';
            document.getElementById('selected_services').value = '';
            document.getElementById('nextBtn').disabled = true;

            // Remove visual selections
            document.querySelectorAll('.service-option, .package-option, .multiple-service-option').forEach(opt => {
                opt.classList.remove('border-salon-gold', 'bg-secondary-700');
            });

            // Clear multiple services checkboxes
            document.querySelectorAll('.service-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });

            updateSelectedServicesSummary();
        }

        // Multiple Services Functions
        function initializeMultipleServicesSelection() {
            document.querySelectorAll('.service-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectedServices();
                });
            });
        }

        function updateSelectedServices() {
            selectedServices = [];
            const checkboxes = document.querySelectorAll('.service-checkbox:checked');

            checkboxes.forEach(checkbox => {
                const serviceOption = checkbox.closest('.multiple-service-option');
                selectedServices.push({
                    id: serviceOption.dataset.serviceId,
                    name: serviceOption.querySelector('h4').textContent.trim(),
                    price: parseFloat(serviceOption.dataset.servicePrice),
                    duration: parseInt(serviceOption.dataset.serviceDuration)
                });
            });

            document.getElementById('selected_services').value = JSON.stringify(selectedServices.map(s => s.id));
            updateSelectedServicesSummary();

            // Enable/disable next button
            document.getElementById('nextBtn').disabled = selectedServices.length === 0;
        }

        function updateSelectedServicesSummary() {
            const summaryDiv = document.getElementById('selectedServicesSummary');
            const listDiv = document.getElementById('selectedServicesList');
            const totalDurationSpan = document.getElementById('totalDuration');
            const totalPriceSpan = document.getElementById('totalPrice');

            if (selectedServices.length === 0) {
                summaryDiv.classList.add('hidden');
                return;
            }

            summaryDiv.classList.remove('hidden');

            // Update services list
            listDiv.innerHTML = selectedServices.map(service => `
                <div class="flex justify-between items-center text-sm">
                    <span class="text-white">${service.name}</span>
                    <span class="text-salon-gold"><?= CURRENCY_SYMBOL ?> ${service.price.toLocaleString()} (${service.duration} min)</span>
                </div>
            `).join('');

            // Calculate totals
            const totalDuration = selectedServices.reduce((sum, service) => sum + service.duration, 0);
            const totalPrice = selectedServices.reduce((sum, service) => sum + service.price, 0);

            totalDurationSpan.textContent = `${totalDuration} minutes`;
            totalPriceSpan.textContent = `<?= CURRENCY_SYMBOL ?> ${totalPrice.toLocaleString()}`;
        }

        // Previous Bookings Functions
        async function loadPreviousBookings() {
            const loadingDiv = document.getElementById('previousBookingsLoading');
            const gridDiv = document.getElementById('previousBookingsGrid');

            loadingDiv.classList.remove('hidden');
            gridDiv.innerHTML = '';

            try {
                const response = await fetch('<?= getBasePath() ?>/api/customer/multiple-services.php?action=previous_bookings');
                const data = await response.json();

                if (data.success) {
                    previousBookings = data.bookings;
                    displayPreviousBookings(data.bookings);
                } else {
                    throw new Error(data.error || 'Failed to load previous bookings');
                }
            } catch (error) {
                console.error('Error loading previous bookings:', error);
                gridDiv.innerHTML = `
                    <div class="col-span-2 text-center py-8 text-gray-400">
                        <i class="fas fa-exclamation-triangle text-2xl mb-4"></i>
                        <p>Error loading previous bookings. Please try again.</p>
                    </div>
                `;
            } finally {
                loadingDiv.classList.add('hidden');
            }
        }

        function displayPreviousBookings(bookings) {
            const gridDiv = document.getElementById('previousBookingsGrid');

            if (bookings.length === 0) {
                gridDiv.innerHTML = `
                    <div class="col-span-2 text-center py-8 text-gray-400">
                        <i class="fas fa-history text-2xl mb-4"></i>
                        <p>No previous bookings found.</p>
                    </div>
                `;
                return;
            }

            gridDiv.innerHTML = bookings.map(booking => `
                <div class="previous-booking-option border border-secondary-600 rounded-lg p-4 cursor-pointer hover:border-salon-gold transition-colors"
                     data-booking-id="${booking.id}"
                     onclick="selectPreviousBooking('${booking.id}')">

                    <div class="flex justify-between items-start mb-3">
                        <h4 class="text-lg font-semibold text-white">
                            ${booking.service_name || booking.package_name || 'Multiple Services'}
                        </h4>
                        <div class="text-right">
                            <div class="text-xl font-bold text-salon-gold"><?= CURRENCY_SYMBOL ?> ${parseInt(booking.total_amount).toLocaleString()}</div>
                            <div class="text-sm text-gray-400">${booking.service_duration || booking.package_duration || 'Multiple'} min</div>
                        </div>
                    </div>

                    <div class="text-sm text-gray-300 mb-3">
                        <div><strong>Staff:</strong> ${booking.staff_name}</div>
                        <div><strong>Date:</strong> ${new Date(booking.date).toLocaleDateString()}</div>
                        ${booking.has_multiple_services ? `<div><strong>Services:</strong> ${booking.services.length} services</div>` : ''}
                    </div>

                    ${booking.has_multiple_services ? `
                        <div class="border-t border-secondary-600 pt-2">
                            <div class="text-xs text-gray-400 mb-1">Included services:</div>
                            <div class="flex flex-wrap gap-1">
                                ${booking.services.map(service => `
                                    <span class="inline-block px-2 py-1 bg-secondary-700 text-xs rounded text-gray-300">
                                        ${service.name}
                                    </span>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        function selectPreviousBooking(bookingId) {
            const booking = previousBookings.find(b => b.id === bookingId);
            if (!booking) return;

            // Remove previous selections
            document.querySelectorAll('.previous-booking-option').forEach(opt => {
                opt.classList.remove('border-salon-gold', 'bg-secondary-700');
            });

            // Add selection to clicked option
            document.querySelector(`[data-booking-id="${bookingId}"]`).classList.add('border-salon-gold', 'bg-secondary-700');

            // Store booking data for recreation
            if (booking.has_multiple_services) {
                selectedServices = booking.services.map(service => ({
                    id: service.service_id,
                    name: service.name,
                    price: parseFloat(service.service_price),
                    duration: parseInt(service.service_duration)
                }));
                document.getElementById('selected_services').value = JSON.stringify(selectedServices.map(s => s.id));
                selectionType = 'multiple_services';
            } else if (booking.service_id) {
                selectedService = {
                    id: booking.service_id,
                    name: booking.service_name,
                    price: parseFloat(booking.total_amount),
                    duration: parseInt(booking.service_duration),
                    type: 'service'
                };
                document.getElementById('service_id').value = selectedService.id;
                selectionType = 'service';
            } else if (booking.package_id) {
                selectedPackage = {
                    id: booking.package_id,
                    name: booking.package_name,
                    price: parseFloat(booking.total_amount),
                    duration: parseInt(booking.package_duration),
                    type: 'package'
                };
                document.getElementById('package_id').value = selectedPackage.id;
                selectionType = 'package';
            }

            document.getElementById('nextBtn').disabled = false;
        }

        // Recurring Booking Functions
        function initializeRecurringOptions() {
            const makeRecurringCheckbox = document.getElementById('makeRecurring');
            const recurringOptions = document.getElementById('recurringOptions');
            const endOption = document.getElementById('endOption');
            const endDateOption = document.getElementById('endDateOption');
            const maxOccurrencesOption = document.getElementById('maxOccurrencesOption');

            makeRecurringCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    recurringOptions.classList.remove('hidden');
                    // Set default end date to 3 months from now
                    const endDate = new Date();
                    endDate.setMonth(endDate.getMonth() + 3);
                    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
                } else {
                    recurringOptions.classList.add('hidden');
                }
            });

            endOption.addEventListener('change', function() {
                if (this.value === 'date') {
                    endDateOption.classList.remove('hidden');
                    maxOccurrencesOption.classList.add('hidden');
                } else {
                    endDateOption.classList.add('hidden');
                    maxOccurrencesOption.classList.remove('hidden');
                }
            });
        }

        // Service selection
        document.querySelectorAll('.service-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.service-option').forEach(opt => {
                    opt.classList.remove('border-salon-gold', 'bg-secondary-700');
                });

                // Add selection to clicked option
                this.classList.add('border-salon-gold', 'bg-secondary-700');

                // Store selected service
                selectedService = {
                    id: this.dataset.serviceId,
                    name: this.querySelector('h4').textContent.trim(),
                    price: parseFloat(this.dataset.servicePrice),
                    duration: parseInt(this.dataset.serviceDuration),
                    type: 'service'
                };

                selectedPackage = null;
                document.getElementById('service_id').value = selectedService.id;
                document.getElementById('package_id').value = '';
                document.getElementById('nextBtn').disabled = false;
            });
        });

        // Package selection
        document.querySelectorAll('.package-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.package-option').forEach(opt => {
                    opt.classList.remove('border-salon-gold', 'bg-secondary-700');
                });

                // Add selection to clicked option
                this.classList.add('border-salon-gold', 'bg-secondary-700');

                // Store selected package
                selectedPackage = {
                    id: this.dataset.packageId,
                    name: this.querySelector('h4').textContent.trim(),
                    price: parseFloat(this.dataset.packagePrice),
                    duration: parseInt(this.dataset.packageDuration),
                    type: 'package'
                };

                selectedService = null;
                document.getElementById('package_id').value = selectedPackage.id;
                document.getElementById('service_id').value = '';
                document.getElementById('nextBtn').disabled = false;
            });
        });

        // Staff selection
        document.querySelectorAll('.staff-option').forEach(option => {
            option.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.staff-option').forEach(opt => {
                    opt.classList.remove('border-salon-gold', 'bg-secondary-700');
                });

                // Add selection to clicked option
                this.classList.add('border-salon-gold', 'bg-secondary-700');

                // Store selected staff
                selectedStaff = {
                    id: this.dataset.staffId,
                    name: this.querySelector('h4').textContent.trim()
                };

                document.getElementById('staff_id').value = selectedStaff.id;
                document.getElementById('nextBtn').disabled = false;
            });
        });

        // Date and time selection with validation
        document.getElementById('date').addEventListener('change', function() {
            validateAvailability();
        });

        document.getElementById('start_time').addEventListener('change', function() {
            calculateEndTime();
            validateAvailability();
        });

        // Points usage calculation
        document.getElementById('points_used').addEventListener('input', function() {
            const pointsUsed = parseInt(this.value) || 0;
            const discount = pointsUsed * 10; // 1 point = TSH 10
            document.getElementById('pointsDiscount').textContent = discount;
            updateSummary();
        });

        function changeStep(direction) {
            const totalSteps = 5;
            const newStep = currentStep + direction;

            if (newStep < 1 || newStep > totalSteps) return;

            // Validate current step before proceeding
            if (direction > 0 && !validateStep(currentStep)) return;

            // Hide current step
            document.getElementById(`step${currentStep}`).classList.add('hidden');
            if (currentStep === totalSteps) {
                document.getElementById('summary').classList.add('hidden');
            }

            // Show new step
            currentStep = newStep;
            
            // If moving to staff selection step (step 2), fetch suggested staff
            if (currentStep === 2) {
                fetchSuggestedStaff();
            }
            
            if (currentStep === totalSteps) {
                document.getElementById('summary').classList.remove('hidden');
                updateSummary();
            } else {
                document.getElementById(`step${currentStep}`).classList.remove('hidden');
            }

            // Update navigation buttons
            updateNavigation();
        }

        function validateStep(step) {
            switch(step) {
                case 1:
                    if (selectionType === 'multiple_services') {
                        if (selectedServices.length === 0) {
                            showNotification('Please select at least one service to continue');
                            return false;
                        }
                    } else if (selectionType === 'previous_booking') {
                        if (!selectedService && !selectedPackage && selectedServices.length === 0) {
                            showNotification('Please select a previous booking to continue');
                            return false;
                        }
                    } else {
                        if (!selectedService && !selectedPackage) {
                            showNotification('Please select a service or package to continue');
                            return false;
                        }
                    }
                    break;
                case 2:
                    if (!selectedStaff) {
                        showNotification('Please select a staff member');
                        return false;
                    }
                    break;
                case 3:
                    const date = document.getElementById('date').value;
                    const startTime = document.getElementById('start_time').value;
                    if (!date || !startTime) {
                        showNotification('Please select both a date and time');
                        return false;
                    }

                    const availabilityStatus = document.getElementById('availabilityStatus');
                    if (availabilityStatus.classList.contains('error')) {
                        showNotification('Selected staff is not available at this time. Please choose a different time or date.');
                        return false;
                    }
                    break;
            }
            return true;
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const submitBtn = document.getElementById('submitBtn');

            // Show/hide previous button
            prevBtn.style.display = currentStep > 1 ? 'block' : 'none';

            // Show/hide next/submit buttons
            if (currentStep === 5) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'block';
            } else {
                nextBtn.style.display = 'block';
                submitBtn.style.display = 'none';
            }
        }

        function calculateEndTime() {
            const startTime = document.getElementById('start_time').value;
            if (!startTime) return;

            let duration = 0;

            if (selectionType === 'multiple_services' && selectedServices.length > 0) {
                // Calculate total duration for multiple services
                duration = selectedServices.reduce((sum, service) => sum + service.duration, 0);
            } else {
                const currentSelection = selectedService || selectedPackage;
                if (!currentSelection) return;
                duration = currentSelection.duration;
            }

            // Calculate end time based on total duration
            const startDateTime = new Date(`2000-01-01T${startTime}:00`);
            const endDateTime = new Date(startDateTime.getTime() + duration * 60000);

            const endTime = endDateTime.toTimeString().slice(0, 5);
            document.getElementById('end_time').value = endTime;
        }

        async function validateAvailability() {
            const date = document.getElementById('date').value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;

            if (!date || !startTime || !selectedStaff) {
                hideAvailabilityStatus();
                return;
            }

            try {
                let response;

                if (selectionType === 'multiple_services' && selectedServices.length > 0) {
                    // Check availability for multiple services
                    const requestBody = {
                        services: selectedServices.map(s => s.id),
                        staff_id: selectedStaff.id,
                        date: date,
                        start_time: startTime
                    };

                    response = await fetch('<?= getBasePath() ?>/api/customer/multiple-services.php?action=check_availability', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody)
                    });
                } else {
                    // Check availability for single service/package
                    const currentSelection = selectedService || selectedPackage;
                    if (!currentSelection) return;

                    const requestBody = {
                        date: date,
                        start_time: startTime,
                        end_time: endTime,
                        staff_id: selectedStaff.id
                    };

                    if (selectedService) {
                        requestBody.service_id = selectedService.id;
                    } else {
                        requestBody.package_id = selectedPackage.id;
                    }

                    response = await fetch('<?= getBasePath() ?>/api/customer/validate-availability.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody)
                    });
                }

                const data = await response.json();
                showAvailabilityStatus(data);

            } catch (error) {
                console.error('Error validating availability:', error);
                showAvailabilityStatus({
                    success: false,
                    message: 'Error checking availability. Please try again.'
                });
            }
        }

        function showAvailabilityStatus(data) {
            const statusDiv = document.getElementById('availabilityStatus');
            const messageDiv = document.getElementById('availabilityMessage');

            statusDiv.classList.remove('hidden');

            if (data.success) {
                statusDiv.classList.remove('error');
                statusDiv.classList.add('success');
                messageDiv.className = 'text-sm p-2 rounded bg-green-100 text-green-700 border border-green-400';
                messageDiv.innerHTML = '<i class="fas fa-check-circle mr-2"></i>' + (data.message || 'Staff is available at this time');
                document.getElementById('nextBtn').disabled = false;
            } else {
                statusDiv.classList.remove('success');
                statusDiv.classList.add('error');
                messageDiv.className = 'text-sm p-2 rounded bg-red-100 text-red-700 border border-red-400';
                messageDiv.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + (data.message || 'Staff is not available at this time');
                document.getElementById('nextBtn').disabled = true;
            }
        }

        function hideAvailabilityStatus() {
            document.getElementById('availabilityStatus').classList.add('hidden');
            document.getElementById('nextBtn').disabled = false;
        }

        function updateSummary() {
            const date = document.getElementById('date').value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;

            if (!selectedStaff || !date || !startTime) return;

            let serviceName, totalPrice, totalDuration;

            if (selectionType === 'multiple_services' && selectedServices.length > 0) {
                serviceName = `${selectedServices.length} Services: ${selectedServices.map(s => s.name).join(', ')}`;
                totalPrice = selectedServices.reduce((sum, service) => sum + service.price, 0);
                totalDuration = selectedServices.reduce((sum, service) => sum + service.duration, 0);
            } else {
                const currentSelection = selectedService || selectedPackage;
                if (!currentSelection) return;

                serviceName = currentSelection.name;
                totalPrice = currentSelection.price;
                totalDuration = currentSelection.duration;
            }

            const pointsUsed = parseInt(document.getElementById('points_used').value) || 0;
            const pointsDiscount = pointsUsed * 10; // 1 point = TSH 10
            const total = Math.max(0, totalPrice - pointsDiscount);

            // Format time display
            const startTimeFormatted = new Date(`2000-01-01T${startTime}:00`).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            const endTimeFormatted = new Date(`2000-01-01T${endTime}:00`).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            const timeDisplay = `${startTimeFormatted} - ${endTimeFormatted}`;

            document.getElementById('summaryService').textContent = serviceName;
            document.getElementById('summaryStaff').textContent = selectedStaff.name;
            document.getElementById('summaryDateTime').textContent =
                new Date(date).toLocaleDateString() + ' at ' + timeDisplay;
            document.getElementById('summaryDuration').textContent = totalDuration + ' minutes';
            document.getElementById('summaryPrice').textContent = '<?= CURRENCY_SYMBOL ?> ' + parseInt(totalPrice).toLocaleString();

            if (pointsUsed > 0) {
                document.getElementById('pointsDiscountRow').style.display = 'flex';
                document.getElementById('summaryPointsDiscount').textContent = '-<?= CURRENCY_SYMBOL ?> ' + parseInt(pointsDiscount).toLocaleString();
            } else {
                document.getElementById('pointsDiscountRow').style.display = 'none';
            }

            document.getElementById('summaryTotal').textContent = '<?= CURRENCY_SYMBOL ?> ' + parseInt(total).toLocaleString();
        }

        // Handle pre-selection with pagination support
        async function handlePreSelection() {
            // Check URL parameters first
            const urlParams = new URLSearchParams(window.location.search);
            const serviceId = urlParams.get('service');
            const packageId = urlParams.get('package');

            // Check sessionStorage for pending bookings
            const pendingServiceBooking = sessionStorage.getItem('pendingServiceBooking');
            const pendingPackageBooking = sessionStorage.getItem('pendingPackageBooking');

            let serviceToSelect = null;
            let packageToSelect = null;

            // Handle service pre-selection
            if (serviceId) {
                serviceToSelect = serviceId;
            } else if (pendingServiceBooking) {
                try {
                    const bookingData = JSON.parse(pendingServiceBooking);
                    if (Date.now() - bookingData.timestamp < 3600000) {
                        serviceToSelect = bookingData.serviceId;
                    }
                    sessionStorage.removeItem('pendingServiceBooking');
                } catch (e) {
                    console.error('Error parsing pending service booking data:', e);
                    sessionStorage.removeItem('pendingServiceBooking');
                }
            }

            // Handle package pre-selection
            if (packageId) {
                packageToSelect = packageId;
            } else if (pendingPackageBooking) {
                try {
                    const bookingData = JSON.parse(pendingPackageBooking);
                    if (Date.now() - bookingData.timestamp < 3600000) {
                        packageToSelect = bookingData.packageId;
                    }
                    sessionStorage.removeItem('pendingPackageBooking');
                } catch (e) {
                    console.error('Error parsing pending package booking data:', e);
                    sessionStorage.removeItem('pendingPackageBooking');
                }
            }

            // Pre-select service if found
            if (serviceToSelect) {
                await handleServicePreSelection(serviceToSelect);
            }
            // Pre-select package if found (packages take priority over services if both are present)
            else if (packageToSelect) {
                await handlePackagePreSelection(packageToSelect);
            }
        }

        // Handle service pre-selection with pagination support
        async function handleServicePreSelection(serviceId) {
            // First check if service is on current page
            const serviceOption = document.querySelector(`[data-service-id="${serviceId}"]`);

            if (serviceOption) {
                // Service found on current page - select it
                switchTab('services');
                setTimeout(() => {
                    serviceOption.click();
                    skipToStaffSelection();
                    showPreSelectionNotification(serviceOption.querySelector('h4').textContent.trim(), 'service', true);
                }, 100);
                return;
            }

            // Service not found on current page - find which page it's on
            try {
                const response = await fetch(`<?= getBasePath() ?>/api/customer/find-service-page.php?service_id=${serviceId}`);
                const data = await response.json();

                if (data.success && data.page) {
                    // Store the service ID for after page load
                    sessionStorage.setItem('pendingServiceSelection', JSON.stringify({
                        serviceId: serviceId,
                        timestamp: Date.now()
                    }));

                    // Navigate to the correct page
                    await navigateToPage(data.page);

                    // After navigation, try selection again
                    setTimeout(() => {
                        const serviceOption = document.querySelector(`[data-service-id="${serviceId}"]`);
                        if (serviceOption) {
                            switchTab('services');
                            setTimeout(() => {
                                serviceOption.click();
                                skipToStaffSelection();
                                showPreSelectionNotification(serviceOption.querySelector('h4').textContent.trim(), 'service', true);
                            }, 100);
                        }
                        // Clean up
                        sessionStorage.removeItem('pendingServiceSelection');
                    }, 500);
                } else {
                    console.warn('Service not found:', serviceId);
                    showNotification('Selected service not found');
                }
            } catch (error) {
                console.error('Error finding service page:', error);
                showNotification('Error loading selected service');
            }
        }

        // Handle package pre-selection with pagination support
        async function handlePackagePreSelection(packageId) {
            // First check if package is on current page
            const packageOption = document.querySelector(`[data-package-id="${packageId}"]`);

            if (packageOption) {
                // Package found on current page - select it
                switchTab('packages');
                setTimeout(() => {
                    packageOption.click();
                    skipToStaffSelection();
                    showPreSelectionNotification(packageOption.querySelector('h4').textContent.trim(), 'package', true);
                }, 100);
                return;
            }

            // Package not found on current page - packages are not paginated currently
            // But we'll add support for future pagination
            console.warn('Package not found on current page:', packageId);
            showNotification('Selected package not found');
        }

        // Navigate to a specific page
        async function navigateToPage(page) {
            return new Promise((resolve, reject) => {
                const servicesSection = document.getElementById('servicesSection');
                servicesSection.style.opacity = '0.5';

                fetch(`${window.location.pathname}?page=${page}`)
                    .then(response => response.text())
                    .then(html => {
                        const temp = document.createElement('div');
                        temp.innerHTML = html;

                        const newServicesSection = temp.querySelector('#servicesSection');
                        servicesSection.innerHTML = newServicesSection.innerHTML;

                        // Update URL without reload
                        window.history.pushState({}, '', `?page=${page}`);

                        servicesSection.style.opacity = '1';

                        // Reinitialize service selection handlers
                        initializeServiceSelection();

                        resolve();
                    })
                    .catch(error => {
                        console.error('Error navigating to page:', error);
                        servicesSection.style.opacity = '1';
                        reject(error);
                    });
            });
        }

        // Skip to staff selection step when service/package is pre-selected
        function skipToStaffSelection() {
            setTimeout(() => {
                document.getElementById('step1').classList.add('hidden');
                document.getElementById('step2').classList.remove('hidden');
                currentStep = 2;
                updateNavigation();
                fetchSuggestedStaff(); // Fetch staff suggestions when skipping to step 2
            }, 200);
        }

        // Function to fetch suggested staff
        async function fetchSuggestedStaff() {
            try {
                const serviceId = document.getElementById('service_id').value;
                const packageId = document.getElementById('package_id').value;
                
                if (!serviceId && !packageId) {
                    console.error('No service or package selected');
                    return;
                }

                const requestData = serviceId 
                    ? { service_id: serviceId }
                    : { package_id: packageId };

                const response = await fetch('<?= getBasePath() ?>/api/customer/suggest-staff.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                
                if (data.success) {
                    updateStaffSection(data.staff);
                } else {
                    console.error('Error fetching staff:', data.message);
                    showNotification('Error fetching staff suggestions. Please try again.');
                }
            } catch (error) {
                console.error('Error fetching suggested staff:', error);
                showNotification('Error fetching staff suggestions. Please try again.');
            }
        }

        // Function to update staff section with suggested staff
        function updateStaffSection(staffList) {
            const staffContainer = document.querySelector('#step2 .grid');
            
            // Clear previous staff selection
            selectedStaff = null;
            document.getElementById('staff_id').value = '';

            staffContainer.innerHTML = staffList.map(member => `
                <div class="staff-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 hover-lift relative"
                     data-staff-id="${member.id}">
                    ${member.is_best_fit ? `
                        <div class="absolute -top-3 -right-3 w-12 h-12 flex items-center justify-center">
                            <div class="absolute inset-0 bg-salon-gold rounded-full animate-ping opacity-20"></div>
                            <div class="absolute inset-0 bg-salon-gold rounded-full"></div>
                            <i class="fas fa-crown text-black text-lg relative z-10"></i>
                        </div>
                    ` : ''}
                    <div class="flex items-center gap-4 mb-4">
                        <div class="w-16 h-16 rounded-full ${member.is_best_fit ? 'bg-salon-gold' : 'bg-secondary-800'} flex items-center justify-center border-2 ${member.is_best_fit ? 'border-salon-gold shadow-lg shadow-salon-gold/20' : 'border-secondary-700'}">
                            <i class="fas fa-user ${member.is_best_fit ? 'text-black' : 'text-gray-400'} text-xl"></i>
                        </div>
                        <div>
                            <div class="flex items-center gap-2">
                                <h4 class="font-semibold text-white text-lg">${member.name}</h4>
                                ${member.is_best_fit ? `
                                    <span class="px-2 py-1 bg-salon-gold/20 text-salon-gold text-xs rounded-full">
                                        Best Match
                                    </span>
                                ` : ''}
                            </div>
                            <p class="text-sm text-salon-gold font-medium">${member.role}</p>
                        </div>
                    </div>

                    ${member.specialties && member.specialties.length > 0 ? `
                        <div class="flex flex-wrap gap-2 mb-3">
                            ${member.specialties.map(specialty => `
                                <span class="inline-block px-3 py-1 bg-secondary-800/50 border ${member.is_best_fit ? 'border-salon-gold/30' : 'border-secondary-700'} text-xs rounded-full ${member.is_best_fit ? 'text-salon-gold' : 'text-gray-300'}">
                                    ${specialty}
                                </span>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `).join('');

            // Sort the DOM elements to put best fit first
            const staffOptions = Array.from(staffContainer.children);
            staffOptions.sort((a, b) => {
                const aIsBestFit = a.querySelector('.fa-crown') !== null;
                const bIsBestFit = b.querySelector('.fa-crown') !== null;
                return bIsBestFit - aIsBestFit;
            });
            staffContainer.innerHTML = '';
            staffOptions.forEach(option => staffContainer.appendChild(option));

            // Reinitialize staff selection event listeners
            document.querySelectorAll('.staff-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.staff-option').forEach(opt => {
                        opt.classList.remove('border-salon-gold', 'bg-secondary-700');
                    });
                    this.classList.add('border-salon-gold', 'bg-secondary-700');
                    
                    selectedStaff = {
                        id: this.dataset.staffId,
                        name: this.querySelector('h4').textContent.trim()
                    };

                    document.getElementById('staff_id').value = selectedStaff.id;
                    document.getElementById('nextBtn').disabled = false;
                });
            });
        }

        function showPreSelectionNotification(itemName, itemType = 'service', skipped = false) {
            // Remove any existing notifications first
            const existingNotifications = document.querySelectorAll('.fixed.top-4.right-4');
            existingNotifications.forEach(notification => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            });

            // Create a temporary notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-salon-gold text-black px-4 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 max-w-sm';

            const message = skipped
                ? `${itemName} ${itemType} pre-selected - Proceeding to staff selection`
                : `${itemName} ${itemType} pre-selected`;

            notification.innerHTML = `
                <div class="flex items-start">
                    <svg class="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-semibold text-sm leading-tight">${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Remove notification after 4 seconds (longer for combined message)
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // Initialize
        updateNavigation();

        // Handle pre-selection when page loads
        document.addEventListener('DOMContentLoaded', function() {
            handlePreSelection();
            // Also check for any pending selections from pagination navigation
            setTimeout(checkPendingSelections, 100);
        });

        // If DOM is already loaded, run immediately
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                handlePreSelection();
                setTimeout(checkPendingSelections, 100);
            });
        } else {
            handlePreSelection();
            setTimeout(checkPendingSelections, 100);
        }

        // Add this to your existing JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Handle pagination clicks
            document.querySelectorAll('.pagination-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = this.dataset.page;

                    // Show loading state
                    const servicesSection = document.getElementById('servicesSection');
                    servicesSection.style.opacity = '0.5';

                    // Fetch new page content
                    fetch(`${window.location.pathname}?page=${page}`)
                        .then(response => response.text())
                        .then(html => {
                            // Create a temporary container
                            const temp = document.createElement('div');
                            temp.innerHTML = html;

                            // Replace services section content
                            const newServicesSection = temp.querySelector('#servicesSection');
                            servicesSection.innerHTML = newServicesSection.innerHTML;

                            // Update URL without reload
                            window.history.pushState({}, '', `?page=${page}`);

                            // Restore opacity
                            servicesSection.style.opacity = '1';

                            // Reinitialize service selection handlers
                            initializeServiceSelection();

                            // Check for pending selections after page load
                            checkPendingSelections();
                        })
                        .catch(error => {
                            console.error('Error fetching page:', error);
                            servicesSection.style.opacity = '1';
                        });
                });
            });
        });

        // Check for pending selections (used after pagination)
        function checkPendingSelections() {
            // Check for pending service selection
            const pendingServiceSelection = sessionStorage.getItem('pendingServiceSelection');
            if (pendingServiceSelection) {
                try {
                    const selectionData = JSON.parse(pendingServiceSelection);
                    if (Date.now() - selectionData.timestamp < 10000) { // 10 second timeout
                        const serviceOption = document.querySelector(`[data-service-id="${selectionData.serviceId}"]`);
                        if (serviceOption) {
                            switchTab('services');
                            setTimeout(() => {
                                serviceOption.click();
                                skipToStaffSelection();
                                showPreSelectionNotification(serviceOption.querySelector('h4').textContent.trim(), 'service', true);
                            }, 100);
                            sessionStorage.removeItem('pendingServiceSelection');
                        }
                    } else {
                        // Timeout - clean up
                        sessionStorage.removeItem('pendingServiceSelection');
                    }
                } catch (e) {
                    console.error('Error parsing pending service selection:', e);
                    sessionStorage.removeItem('pendingServiceSelection');
                }
            }
        }

        // Function to initialize service selection
        function initializeServiceSelection() {
            document.querySelectorAll('.service-option').forEach(option => {
                option.addEventListener('click', function() {
                    // Your existing service selection logic
                    document.querySelectorAll('.service-option').forEach(opt => {
                        opt.classList.remove('border-salon-gold', 'bg-secondary-800/50');
                    });
                    this.classList.add('border-salon-gold', 'bg-secondary-800/50');
                    
                    document.getElementById('service_id').value = this.dataset.serviceId;
                    document.getElementById('package_id').value = '';
                });
            });
        }

        // Initialize service selection on page load
        initializeServiceSelection();

        // Initialize search and filter functionality
        function initializeSearchAndFilter() {
            const searchInput = document.getElementById('searchInput');
            const categoryFilter = document.getElementById('categoryFilter');
            const activeFilters = document.getElementById('activeFilters');
            const servicesSection = document.getElementById('servicesSection');
            const packagesSection = document.getElementById('packagesSection');

            // Search functionality
            async function performSearch() {
                const searchTerm = searchInput.value.trim();
                const selectedCategory = categoryFilter.value;

                // If no search terms, restore original pagination
                if (!searchTerm && !selectedCategory) {
                    restorePagination();
                    return;
                }

                // Show loading state
                servicesSection.style.opacity = '0.5';
                packagesSection.style.opacity = '0.5';

                try {
                    // Build query parameters
                    const params = new URLSearchParams();
                    if (searchTerm) params.append('search', searchTerm);
                    if (selectedCategory) params.append('category', selectedCategory);

                    // Fetch search results from API
                    const response = await fetch(`<?= getBasePath() ?>/api/customer/search-services.php?${params.toString()}`);
                    const data = await response.json();

                    if (data.success) {
                        // Update services section
                        const servicesHTML = data.services.map(service => `
                            <div class="service-option border border-secondary-700 rounded-xl p-6 cursor-pointer hover:border-salon-gold hover:bg-secondary-800/50 transition-all duration-300 hover-lift"
                                 data-service-id="${service.id}"
                                 data-service-price="${service.price}"
                                 data-service-duration="${service.duration}"
                                 data-type="service">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-semibold text-white text-lg">${service.name}</h4>
                                    <span class="text-salon-gold font-bold text-lg"><?= CURRENCY_SYMBOL ?> ${parseFloat(service.price).toLocaleString()}</span>
                                </div>
                                <p class="text-sm text-gray-400 mb-4 leading-relaxed">${service.description}</p>
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <span class="flex items-center"><i class="fas fa-clock mr-2 text-salon-gold"></i>${service.duration} minutes</span>
                                    <span class="flex items-center"><i class="fas fa-tag mr-2 text-salon-gold"></i>${service.category}</span>
                                </div>
                            </div>
                        `).join('');

                        // Update packages section
                        const packagesHTML = data.packages.map(package => `
                            <div class="package-option border border-secondary-600 rounded-lg p-4 cursor-pointer hover:border-salon-gold transition-colors"
                                 data-package-id="${package.id}"
                                 data-package-price="${package.price}"
                                 data-package-duration="${package.total_duration}"
                                 data-type="package">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-white">${package.name}</h4>
                                    <div class="text-right">
                                        <span class="text-salon-gold font-semibold"><?= CURRENCY_SYMBOL ?> ${parseFloat(package.price).toLocaleString()}</span>
                                        ${package.savings > 0 ? `
                                            <div class="text-xs text-green-400">Save <?= CURRENCY_SYMBOL ?> ${parseFloat(package.savings).toLocaleString()}</div>
                                        ` : ''}
                                    </div>
                                </div>

                                ${package.description ? `
                                    <p class="text-sm text-gray-400 mb-2">${package.description}</p>
                                ` : ''}

                                <div class="mb-2">
                                    <p class="text-xs text-gray-500 mb-1">Includes:</p>
                                    <div class="flex flex-wrap gap-1">
                                        ${package.services.map(service => `
                                            <span class="inline-block px-2 py-1 bg-secondary-700 text-xs rounded text-gray-300">
                                                ${service.name}
                                            </span>
                                        `).join('')}
                                    </div>
                                </div>

                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span><i class="fas fa-clock mr-1"></i>${package.total_duration} minutes total</span>
                                    <span><i class="fas fa-box mr-1"></i>${package.services.length} services</span>
                                </div>
                            </div>
                        `).join('');

                        // Update the DOM
                        const servicesContainer = servicesSection.querySelector('.grid');
                        const packagesContainer = packagesSection.querySelector('.grid');

                        servicesContainer.innerHTML = servicesHTML || '<div class="col-span-full text-center py-8 text-gray-400">No services found matching your search.</div>';
                        packagesContainer.innerHTML = packagesHTML || '<div class="col-span-full text-center py-8 text-gray-400">No packages found matching your search.</div>';

                        // Hide pagination when searching
                        hidePagination();

                        // Update active filters display
                        updateActiveFilters(searchTerm, selectedCategory);

                        // Reinitialize click handlers
                        initializeServiceSelection();
                        initializePackageSelection();
                    } else {
                        showNotification('Error fetching search results');
                    }
                } catch (error) {
                    console.error('Search error:', error);
                    showNotification('Error performing search');
                } finally {
                    // Restore opacity
                    servicesSection.style.opacity = '1';
                    packagesSection.style.opacity = '1';
                }
            }

            // Function to restore original pagination
            async function restorePagination() {
                try {
                    // Get current page from URL
                    const currentPage = new URLSearchParams(window.location.search).get('page') || '1';

                    // Show loading state
                    servicesSection.style.opacity = '0.5';

                    // Fetch the current page content
                    const response = await fetch(`${window.location.pathname}?page=${currentPage}`);
                    const html = await response.text();

                    // Create a temporary container
                    const temp = document.createElement('div');
                    temp.innerHTML = html;

                    // Replace services section content
                    const newServicesSection = temp.querySelector('#servicesSection');
                    servicesSection.innerHTML = newServicesSection.innerHTML;

                    // Clear active filters
                    activeFilters.innerHTML = '';

                    // Show pagination controls
                    showPagination();

                    // Restore opacity
                    servicesSection.style.opacity = '1';

                    // Reinitialize service selection handlers
                    initializeServiceSelection();

                    // Reinitialize pagination handlers
                    initializePaginationHandlers();

                } catch (error) {
                    console.error('Error restoring pagination:', error);
                    // Fallback to page reload
                    const currentPage = new URLSearchParams(window.location.search).get('page') || '1';
                    window.location.href = `${window.location.pathname}?page=${currentPage}`;
                }
            }

            // Function to hide pagination controls
            function hidePagination() {
                const paginationControls = servicesSection.querySelector('.mt-8.flex.justify-center');
                if (paginationControls) {
                    paginationControls.style.display = 'none';
                }
            }

            // Function to show pagination controls
            function showPagination() {
                const paginationControls = servicesSection.querySelector('.mt-8.flex.justify-center');
                if (paginationControls) {
                    paginationControls.style.display = 'flex';
                }
            }

            // Function to initialize pagination handlers
            function initializePaginationHandlers() {
                document.querySelectorAll('.pagination-btn').forEach(btn => {
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        const page = this.dataset.page;

                        // Show loading state
                        const servicesSection = document.getElementById('servicesSection');
                        servicesSection.style.opacity = '0.5';

                        // Fetch new page content
                        fetch(`${window.location.pathname}?page=${page}`)
                            .then(response => response.text())
                            .then(html => {
                                // Create a temporary container
                                const temp = document.createElement('div');
                                temp.innerHTML = html;

                                // Replace services section content
                                const newServicesSection = temp.querySelector('#servicesSection');
                                servicesSection.innerHTML = newServicesSection.innerHTML;

                                // Update URL without reload
                                window.history.pushState({}, '', `?page=${page}`);

                                // Restore opacity
                                servicesSection.style.opacity = '1';

                                // Reinitialize service selection handlers
                                initializeServiceSelection();

                                // Reinitialize pagination handlers
                                initializePaginationHandlers();

                                // Check for pending selections after page load
                                checkPendingSelections();
                            })
                            .catch(error => {
                                console.error('Error fetching page:', error);
                                servicesSection.style.opacity = '1';
                            });
                    });
                });
            }

            // Update active filters display
            function updateActiveFilters(searchTerm, category) {
                activeFilters.innerHTML = '';
                
                if (searchTerm) {
                    const searchFilter = createFilterTag(`Search: ${searchTerm}`, () => {
                        searchInput.value = '';
                        performSearch();
                    });
                    activeFilters.appendChild(searchFilter);
                }

                if (category) {
                    const categoryFilterTag = createFilterTag(`Category: ${category}`, () => {
                        categoryFilter.value = '';
                        performSearch();
                    });
                    activeFilters.appendChild(categoryFilterTag);
                }
            }

            // Create filter tag element
            function createFilterTag(text, onRemove) {
                const tag = document.createElement('div');
                tag.className = 'inline-flex items-center bg-salon-gold/20 text-salon-gold px-3 py-1 rounded-full text-sm';
                tag.innerHTML = `
                    ${text}
                    <button class="ml-2 focus:outline-none" aria-label="Remove filter">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                tag.querySelector('button').addEventListener('click', onRemove);
                return tag;
            }

            // Event listeners
            searchInput.addEventListener('input', debounce(performSearch, 300));
            categoryFilter.addEventListener('change', performSearch);

            // Initialize click handlers
            function initializeServiceSelection() {
                document.querySelectorAll('.service-option').forEach(option => {
                    option.addEventListener('click', function() {
                        document.querySelectorAll('.service-option').forEach(opt => {
                            opt.classList.remove('border-salon-gold', 'bg-secondary-800/50');
                        });
                        this.classList.add('border-salon-gold', 'bg-secondary-800/50');
                        
                        selectedService = {
                            id: this.dataset.serviceId,
                            name: this.querySelector('h4').textContent.trim(),
                            price: parseFloat(this.dataset.servicePrice),
                            duration: parseInt(this.dataset.serviceDuration),
                            type: 'service'
                        };

                        selectedPackage = null;
                        document.getElementById('service_id').value = selectedService.id;
                        document.getElementById('package_id').value = '';
                        document.getElementById('nextBtn').disabled = false;
                    });
                });
            }

            function initializePackageSelection() {
                document.querySelectorAll('.package-option').forEach(option => {
                    option.addEventListener('click', function() {
                        document.querySelectorAll('.package-option').forEach(opt => {
                            opt.classList.remove('border-salon-gold', 'bg-secondary-800/50');
                        });
                        this.classList.add('border-salon-gold', 'bg-secondary-800/50');
                        
                        selectedPackage = {
                            id: this.dataset.packageId,
                            name: this.querySelector('h4').textContent.trim(),
                            price: parseFloat(this.dataset.packagePrice),
                            duration: parseInt(this.dataset.packageDuration),
                            type: 'package'
                        };

                        selectedService = null;
                        document.getElementById('package_id').value = selectedPackage.id;
                        document.getElementById('service_id').value = '';
                        document.getElementById('nextBtn').disabled = false;
                    });
                });
            }

            // Only perform search if there are active filters
            // Don't auto-search on page load to preserve pagination
            if (searchInput.value.trim() || categoryFilter.value) {
                performSearch();
            }
        }

        // Debounce function for search input
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Initialize search and filter when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeSearchAndFilter();
            initializeMultipleServicesSelection();
            initializeRecurringOptions();
        });

        // Add these functions at the start of your script section
        function showNotification(message) {
            const notification = document.getElementById('notification');
            const messageEl = document.getElementById('notificationMessage');
            messageEl.textContent = message;
            
            // Show notification
            notification.classList.remove('translate-x-full', 'opacity-0');
            
            // Auto-hide after 3 seconds
            setTimeout(hideNotification, 3000);
        }

        function hideNotification() {
            const notification = document.getElementById('notification');
            notification.classList.add('translate-x-full', 'opacity-0');
        }
    </script>

<style>
/* Add these styles to your existing CSS */
.pagination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    padding: 0 0.75rem;
    border: 1px solid #374151;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
}

.pagination-btn:hover {
    border-color: #f59e0b;
    color: #f59e0b;
    background-color: rgba(245, 158, 11, 0.1);
}

.pagination-btn.active {
    background-color: #f59e0b;
    border-color: #f59e0b;
    color: #000000;
}

/* Search and Filter Styles */
.service-option,
.package-option {
    transition: all 0.3s ease-in-out;
}

.service-option:not([style*="display: none"]),
.package-option:not([style*="display: none"]) {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Active Filter Tags */
#activeFilters > div {
    transition: all 0.2s ease-in-out;
}

#activeFilters > div:hover {
    background-color: rgba(245, 158, 11, 0.3);
}

#activeFilters button:hover i {
    transform: rotate(90deg);
    transition: transform 0.2s ease-in-out;
}

/* Search Input Focus Effect */
#searchInput:focus + div i {
    color: #f59e0b;
    transition: color 0.2s ease-in-out;
}

/* No Results Message Animation */
.no-results-message {
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Grid Layout Improvements */
.grid {
    grid-auto-rows: minmax(min-content, max-content);
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style>

<?php include __DIR__ . '/../../includes/customer_footer.php'; ?>
